{"EMAIL_ALREADY_EXISTS_EXCEPTION": "This e-mail address is already in use. Try a different one or log in", "URL_IS_NOT_VALID_EXCEPTION": "Enter correct link", "unpublished_job_cant_be_selected": "Unpublished jobs Can’t be selected", "INVALID_USERNAME_FORMAT_EXCEPTION": "This username format is not valid", "USERNAME_ALREADY_EXISTS_EXCEPTION": "This username is not valid, try a different one", "USERNAME_TOO_LONG_EXCEPTION": "This username is too long, maximum 30 characters", "USERNAME_TOO_SHORT_EXCEPTION": "This username is too short, minimum 5 characters", "INVALID_PAGE_USERNAME_FORMAT_EXCEPTION": "This username format is not valid", "PAGE_USERNAME_ALREADY_EXISTS_EXCEPTION": "This username is not valid, try a different one", "wrong_email_or_password": "Wrong email or password", "wrong_password": "Wrong password", "wrong_email_or_password_hint": "Try again with correct email and password", "CLIENT_ERROR": "Any non-specific 400 series error", "SERVER_ERROR": "Any 500 series error", "TIMEOUT_ERROR": "Server did not respond in time", "CONNECTION_ERROR": "Server not available, bad dns", "NETWORK_ERROR": "Network error", "pls_check_y_int_connec": "please check your internet connection", "CANCEL_ERROR": "Request has been cancelled", "PHONE_PATTERN_IS_NOT_VALID_EXCEPTION": "Phone pattern is not valid", "un_predictable_error": "Unpredictable error", "went_wrong_msg": "Please refresh the page and try again", "reload_page": "Reload page", "reach_max_error": "You have reached the maximum limit", "PageUsernameAlreadyExistsException": "A page with this username already exists", "WrongPasswordException": "Password is wrong", "UserIsBlockedException": "User is blocked", "PageAccessException": "You do not have required permissions for this action", "RateLimiterExceedException": "Too many password reset requests, please try again 1 hour later", "EmailNotFoundException": "We could not find your account, try with a different email", "AccessDeniedException": "Access is denied", "DuplicateRecommendationForSameRoleException": "You have already written a recommendation for this position", "with": "with", "other": "other", "others": "others", "shared_a": "shared a", "is_in": "is in", "liked_this": "liked this", "boosted_this": "boosted this", "celebrated_this": "celebrated this", "cmnt_on_this": "commented on this", "cmnt_on_your": "commented on your", "q_mrk": "?", "related_topics": "Related topics", "give_feedback": "Give a feedback", "feedback_desc": "We always care about your thoughts. tell us about what we can improve", "faq_full_abbr": "Frequently asked questions - FAQ", "how_v_help_u": "How can we help you?", "login_password": "Login & Password", "account_settings": "Account settings", "do_u_need_help": "Do you need help?", "faq_full": "Frequently asked questions", "get_prof_help": "Get professional help", "topics": "Topics", "payments": "Payments", "support": "Support", "business_support": "Business support", "business_support_m": "If your business goal is to create a sustainable business relationships, address a larger group of people or businesses, utilizing human ingenuity and Lobox's intelligent technology, we stand by your side", "advance_search": "Advance search", "professional_search": "Professional search", "unlimited_messages": "Unlimited messages", "professional_support": "Professional support", "job_listing": "job listing", "enterprise": "Enterprise", "professional": "Professional", "coming_soon": "Coming soon", "monthly": "Monthly", "annually": "Annually", "annually_paid": "Paid annually", "month_abbr": "mo", "pricing_m": "Since the fact Lobox is an intersection of human value and value proposition; be part of this value and present your business on one of the ways connecting to this intersection", "business_tools_sm": "Business tools", "business_tools_sm_m": "Nowadays, more advance technology means easier reach potential talents and customers. In fact, because of increased connectivity, it’s never been easier to reach both of them", "advance_search_m": "Sometimes, quality lies in nuances and details and mostly, quality is a subtle substance that does not emerge in the first stage, and requires us to dig deeper patiently", "job_listing_m": "If your business aims to be at it is best, start creating well rounded job posts to hire right people because they are your most valuable assets", "lobox_business": "Lobox business", "lobox_business_m": "Lobox adds value to both this ecosystem and your business, and is a great platform for those who want to share their inspiration", "for_profs": "FOR PROFESSIONALS", "try_4_free": "Try for free", "v_r_stronger": "What makes us stronger?", "v_r_stronger_m": "At Lobox, the work culture and core values are essential. We know that teamwork can beat distance, we share the need for self-development, and believe that at Lobox you can be whoever you want to be", "see_open_pos": "See open positions", "v_r_lobox": "We are Lobox", "v_r_lobox_m": "We are a team of visionaries, innovators, and trailblazers dedicated to transforming the world of professional networking. Our vision is to empower individuals and businesses by providing a platform that seamlessly bridges the gap between talent and opportunity. At Lobox, we go beyond the limitations of traditional networks, unlocking a world of endless possibilities. Through our intuitive web app, professionals can showcase their skills, fostering meaningful connections that transcend borders. We work remotely, embracing the power of virtual collaboration, and our global team's dedication and perseverance have brought this extraordinary product to life. Join us as we redefine professional networking, shaping the future of work. Welcome to Lobox, where innovation knows no bounds", "remote": "Remote", "full_time": "Full-time", "meet_the_team": "Meet the team", "meet_the_team_m": "Our greatest strength is to be a unique team with the diversity of knowledge and experience, shared values, culture and goals", "scroll_down": "Scroll down", "lobox_proverb": "Think outside the box!", "lobox_proverb_desc": "Great works come with love of what you do. If you look for greatness, let us find what you love", "popular_topics": "Popular topics", "user_agreement": "User agreement", "go_to": "Go to", "who_v_r": "Who we are", "who_v_r_desc": "We are a team that dedicated to create a better enviroment to help to grow your career, find a community to your own and respectful to your privacy", "our_values": "Our values", "about_us": "About us", "our_story": "Our story", "community": "Community", "community_m": "The true power of the social media platforms are users and the communities that makes them the real owners of it. We are aware of this fact and putting our users to top priority of everything", "transparency": "Transparency", "transparency_m": "In Lobox we care to be transparent about how we are handling the data that you are sharing with us. As we are not allowing ads it means that your data is only stay in between you and us. No third-party can reach and process your personal information", "career": "Career", "career_m": "Lobox is an emerging platform to focus on to create a better social media culture. We are always looking for a chance to grow bigger and represent everyone. If you wanna be a part of it, we would like to meet and talk what we can do together", "team": "Team", "security_privacy": "Security & Privacy", "security_privacy_m": "The key to create a better and a trustworthy environment must be based on the security and privacy of it’s users. You can see and learn how to keep your privacy secure", "privacy_security_cap": "Privacy & Security", "privacy_security": "Privacy & Security", "guidelines": "Guidelines", "privacy_terms": "Privacy & Terms", "about_cookies": "About cookies", "security": "Security", "help_center": "Help center", "help_center_cap": "Help center", "help_desk": "Help desk", "help": "Help", "account": "Account", "your_data": "Your data", "faq": "FAQ", "business_solutions": "Business solutions", "products": "Products", "pricing": "Pricing", "search_placeholder": "Search", "results": "Results", "already_member": "Already a member?", "welcome_note": "Welcome back", "not_member": "Not a member?", "recover_my_password": "Recover my password", "change_password": "Change password", "has_no_pass": "Since you signed up lobox through a social login, you should create a password first", "create_password": "Create password", "reset_password": "Reset password", "forget_password_q": "Forgot password?", "log_in": "log in", "login_cap": "Log in", "login": "<PERSON><PERSON>", "sign_up": "sign up", "signup_cap": "Sign up", "signup": "Sign up", "email_star": "Email", "email_helper": "Example: <EMAIL>", "email_helper_reset_pass": "Enter your email address registered in Lobox", "and": "and", "sign_up_with_social": "or sign up with the following", "linkedin_sign_up": "Sign up with Linkedin", "google": "Google", "google_sign_up": "Sign up with Google", "sign_up_for_lobox": "Sign up", "name_star": "Name", "name": "Name", "job_title": "Job title", "job_id": "Job ID", "birthday": "Birthday", "name_helper": "Example: <PERSON>", "surname_star": "Surname", "surname_helper": "Example: <PERSON>", "job_star": "Job title", "job_hint": "Example: UX designer, salesman, teacher", "country_star": "Country", "country_helper": "Example: United States", "city_star": "City / District", "city_helper": "Example: New York", "continue": "Continue", "enter_code": "Enter the code in your email", "let_us_know": "If this is your email, click the verify button sent to your email, otherwise let us know", "didnt_receive": "Did not receive any email?", "click_resend": "Click to resend verification email", "verification_code": "Verification code", "change_and_resend": "Change and resend", "password_star": "Password", "password_helper": "6 or more characters including letters and numbers", "confirm_password": "Confirm password", "confirm_password_hint": "Both passwords must match", "by_clicking": "Clicking 'Agree and continue' button means you agree to Lobox's privacy policy and cookie policy", "privacy_policy": "Privacy policy", "cookie": "Cookie policy", "agree": "Agree and continue", "review_activity": "View activity", "review_activity_hint": "We detected that you received a wrong verification email and removed your email address from the system. If you think there is a mistake, you can try to login or sign up", "remember_me": "Remember me", "new_to_lobox": "New to Lobox?", "login_for_lobox": "or log in with the following", "google_login": "Log in with Google", "linkedin_login": "Log in with <PERSON><PERSON><PERSON>", "copy_right": "©", "privacy": "Privacy", "terms": "Terms", "cookies": "Cookies", "more": "More", "more_sm": "more", "email_sent": "Email sent", "v_e_send_to": "Verification email sent to", "check_your_email": "Check your email", "check_your_email_hint": "We've sent you an email to show how you can change your password. You can easily change your password by following the instructions in the email", "click_resend_recovery": "Click to resend recovery email", "resend_recovery": "Resend recovery email", "resend_recovery_success": "Recovery email is sent successfully", "Verify_your_email": "Verify your email", "we_have_sent_another_email": "We have sent another email to", "we_have_sent_another_email_hint": "Emails can sometimes be delivered with delay. While we are preparing your account, you can wait a little longer and check your spam folder", "still_nothing": "Still nothing?", "contact_with_us": "Contact with us", "welcome_title1": "Welcome to Lobox", "welcome_hint1": "Think outside the box!", "welcome_title2": "Lobox for everyone", "welcome_hint2": "Although Lobox is a platform for the business world, that is a place for everyone", "welcome_title3": "Let’s create something amazing", "welcome_hint3": "Share the important steps of your career, experience or knowledge with everyone. Let the community grow with you", "share_imp_moments": "Share important moments with your followers", "next": "Next", "skip": "<PERSON><PERSON>", "view_jobs": "View jobs", "all": "All", "clear_all": "Clear all", "recent_searches": "Recent searches", "recent_search": "Recent search", "search_filters": "Search filters", "u_blocked": "You have blocked", "blocking_reasons": "You will no longer be able to access each other's content and receive messages", "compare_to": "Compared to last", "discover_people": "Discover people", "discover_page": "Discover pages", "empty_following_people": "You do not follow any person yet", "empty_saved_jobs": "You do not have a job posting saved. Discover job postings suitable for you now", "empty_applied_jobs": "You do not have a job posting for which you have applied. Discover job postings suitable for you now", "y_a_n_f_any": "You do not follow anyone", "empty_following_page": "You are not following any page", "y_d_n_h_a_f": "You do not have any follower", "y_c_t_c_inter": "your contacts to create interactions", "no_suggestion": "There is no suggestion to show", "create_ur_ps": "You can create pages for your business, institutions or yourself to build your community", "pages_u_follow": "Pages you follow", "total": "total", "pending_requests": "Pending requests", "pending_requests_cap": "Pending requests", "top_suggestions_cap": "Top suggestions", "top_suggestions": "Top suggestions", "p_u_follow": "People you follow", "new_follower": "New follower", "no_data": "No data to show", "filter_by": "Filter by:", "active_jobs": "Active jobs", "active_job": "Active job", "total_jobs": "Total job listings", "total_job": "Total job listing", "apps_need_review": "Applications need to review", "app_need_review": "Application needs to review", "total_app": "Total application", "total_apps": "Total applications", "collection_es_msg": "You have not saved any posts in collection", "collection_es_msg_else": "has not saved any post in this collection", "collection_es_msg_b": "No item is saved in this collection yet", "collection_hint": "You can save posts to your collection of choice", "create_ur_collection": "Collections help you organize the posts you want to save", "create_collection": "Create collection", "y_c_save_post_t_y_coll": "You can save posts to your collections", "post_svd_to_coll": "Post saved to collections", "view_collection": "View collection", "collection_name": "Give your collection a name", "collection_name_example": "Example: Life, foods, business", "create_ur_page": "Create a page for your company, institution or as a public figure", "share_ur_thoughts": "Share your thoughts, photos and videos with your followers", "create_post": "Create post", "all_posts": "All posts", "remove_collection_m": "Are you sure you want to remove this collection?", "remove_collection": "Remove collection", "edit_collection": "Edit collection", "highlights": "Highlights", "highlight_sm": "highlight", "new_articles": "New articles", "pp_category": "Popular categories", "items": "Items", "item": "<PERSON><PERSON>", "h_n_used": "this is not in use", "people_r_talking": "people are talking about this", "person_r_talking": "person is talking about this", "search": "Search", "see_all": "See all", "see_more": "See more", "see": "See", "manage_page": "Manage page", "your_pages": "Your pages", "pages_u_manage": "Pages you manage", "collections": "Collections", "save_to_collections": "Save to collections", "collection_detail": "Collection detail", "hashtags": "Hashtags", "hashtags_sm": "hashtags", "hashtag": "Hashtag", "feed": "Posts", "no_content": "This content is no longer available", "of": "of", "off_cap": "Off", "show": "Show", "showing": "Showing", "post_preview": "Post preview", "unfollow_question_name": "Are you sure you want to unfollow <b>{name}</b>?", "unfollow_hashtag": "Unfollow hashtag", "unfollow": "Unfollow", "unfollow_page": "Unfollow page", "views": "Views", "views_cap": "Views", "searchers_key": "Keywords your searchers used", "searchers_at": "Where your searchers work at?", "others_cap": "Others", "recruiters": "Recruiters", "research_fellow": "Research Fellow", "business_strategist": "Business Strategist", "creative_designer": "Creative Designer", "founder": "Founder", "searchers_do": "What your searchers do?", "search_boundary": "August 25 - September 1", "searched_times": "time your profile appeared in search results between", "weekly_search": "Weekly search stats", "keep_as_secret": "We do not share who viewed your profile with anyone", "profile_views": "Profile views", "page_view": "Page view", "page_views": "Page views", "search_results": "Search results", "applied_job": "Applied by you", "job_applications": "Jobs applications", "applications": "Applications", "application": "Application", "post_views": "Post views", "post_view": "Post view", "weekly": "Weekly", "all_time": "All time", "view": "View", "date": "Date", "date_star": "Date", "title": "Title", "top_posts": "Top posts", "post": "Post", "post_sm": "post", "posts": "Posts", "no_post_saved": "No post saved yet", "no_post_created": "No post has been created yet", "comment": "Comment", "comments": "Comments", "shares": "Shares", "share": "Share", "reaction": "Reaction", "reactions": "Reactions", "post_integrations": "Post interactions", "today": "24h", "7_days": "7 Days", "30_days": "30 Days", "90_days": "90 Days", "enter_valid_email": "Enter a valid email address. “<EMAIL>”", "enter_valid_phone_n": "Enter a valid phone number", "req_email": "Enter your email", "req_password": "Enter your password", "password_length": "Password should be at least 6 char", "password_not_match": "Passwords did not match", "req_jobTitle": "Enter your job title", "req_dateOfBirth": "Enter your Date of birth", "min_dateOfBirth": "You must be minimum 16 years old", "req_lastName": "Enter your Last name", "req_firstName": "Enter your First name", "req_username": "Enter your Username", "req_country": "Select your country", "req_cityDistrict": "Select your city / district", "edit_details": "Edit details", "general_info": "General info", "username_star": "Username", "date_of_birth": "Date of birth", "links": "Links", "website": "Website", "website_helper": "Example: http://example.com", "contact_detail": "Contact details", "contact": "Contact", "contacts": "Contacts", "code": "code", "phone": "Phone", "jobTitle_helper": "Example: UX designer, salesman, teacher", "job_details": "Job details", "write_your_bio": "Write your biography", "write_your_bio_helper": "Tell us about yourself", "experience": "Experience", "experience_helper": "Share your experiences", "accomplishment": "Accomplishment", "accomplishments": "Accomplishments", "ask_for_recommendation": "Ask for recommendation", "ask_for_a_recommendation": "Ask for a recommendation", "ask_for_recommendation_helper": "", "ask": "Ask", "resume": "Resume", "resume_is_required": "Resume is required", "skills": "Skills", "language": "Language", "languages": "Languages", "add_section": "Add section", "bio": "Bio", "add_bio": "Add bio", "bio_helper": "Write your short resume", "empty_bio": "has not added any information yet", "bio_max_length": "Must be less than 4800 characters", "must_be_less_max_len": "Must be less than 3000 characters", "must_be_less": "Must be less than", "characters": "characters", "long_post_error_1": "This status cannot be longer than 63,206 characters; however, it is currently", "long_post_error_2": "characters long. Please shorten it a little more and try again", "add_experience": "Add experience", "edit_experience": "Edit experience", "delete_experience": "Delete this experience", "profile_view": "Profile view", "view_profile": "View profile", "search_result": "Search result", "jobs_you_applied": "Applied by you", "open_to_job": "Open for job opportunities", "discover": "Discover", "analytics": "Analytics", "about": "About", "dark_theme": "Dark theme", "logout": "log out", "logout_cap": "Log out", "private": "Private", "role_private": "You cannot do this due to this person's privacy settings", "public": "Public", "not_found_profile_title": "This profile does not exist", "not_found_profile_sub_title": "We could not reach the person or the page you were looking for. You can either try again or return to the home page", "enter_valid_url": "Enter correct URL", "enter_valid_url_exm": "Enter correct URL, eg: http://example.com", "view_profile_image": "View profile image", "view_cover_image": "View cover image", "edit_cover_image": "Edit cover image", "delete_cover_image": "Delete cover image", "edit_profile_image": "Edit profile image", "delete_profile_image": "Delete profile image", "edit_avatar_img": "Edit avatar image", "upload_another": "Upload another one", "save": "Save", "unsave": "Unsave", "remove": "Remove", "delete": "Delete", "delete_cover_image_hint": "Are you sure you want to delete your cover image?", "delete_profile_image_hint": "Are you sure you want to delete your profile image?", "view_image": "View image", "delete_image": "Delete image", "edit_image": "Edit image", "location": "Location", "req_url": "Enter your URL", "share_profile_via_message": "Share via message", "share_profile_via": "Share profile via", "edit_profile": "Edit profile", "recommend": "Recommend", "block": "Block", "report": "Report", "click_to_add_birthday": "Click to add birthday", "click_to_add_phone": "Click to add phone", "click_to_add_links": "Click to add links", "add_link": "Add link", "click_to_add_email": "Click to add email", "click_to_add_birth_day": "Click to add birthday", "email_address": "Email address", "date_of_birth_helper": "Date of birth", "gender": "Gender", "dont_share": "Do not want to share", "feed_detail": "Feed detail", "unavailable": "Unavailable", "settings": "Settings", "setting": "Setting", "cover_photo": "Cover picture", "profile_photo": "Profile picture", "current_exp": "Current experience", "past_exp": "Past experience", "honor_aw": "Honor & Awards", "ur_p_v_non_l": "Your profile’s visibility to non-lobox members", "profile_info": "Profile information", "career_info": "Career information", "other_options": "Other options", "n_u_etc": "Name, username, number of followers & following", "switch_to": "Switch to", "invite_people": "Invite people", "invite_email_ph": "Write your friend’s email addresses", "invite_email_help": "You can write up to 10 email addresses", "enter_email_msg": "Enter your friends email here to send them invitation:", "invite_via_email": "Invite your friends directly via email", "school": "School", "add_school": "Add school", "delete_school_helper": "Are you sure you want to delete your school?", "edit_bio": "Edit bio", "write_your_short_resume": "Write your short resume", "course": "Course", "edit_course": "Edit course", "delete_course": "Delete course", "add_course": "Add course", "delete_course_helper": "Are you sure you want to delete your course?", "delete_bio": "Delete bio", "delete_bio_helper": "Are you sure you want to delete your bio?", "add": "Add", "delete_birthDate": "Delete birthday", "delete_birthDate_helper": "Are you sure you want to delete your date of birth?", "ask_for_revision": "Ask for revision", "ask_for_a_revision": "Ask for a revision", "ask_for_revision_input_label": "Your message to the person you are recommended", "send": "Send", "sent": "<PERSON><PERSON>", "send_invite": "Send invitation", "decline": "Decline", "accept": "Accept", "empty_received_recommendation": "does not have any received recommendation", "empty_given_recommendation": "does not have any given recommendation", "empty_requests_recommendation": "You do not have any request", "update_recommendation": "Update recommendation", "revise": "Revise", "new_reactions": "New reactions", "new_comments": "New comments", "n_r_2_ur_comment": "New reactions to your comment", "new_replies": "New replies", "n_c_f_post_u_c": "New comments from the posts that you commented", "new_shares": "New shares", "tags": "Tags", "mentions": "Mentions", "up_meetings": "Upcoming meetings", "up_birthdays": "Upcoming birthdays", "hashtag_posts_count": "{count} posts", "new_posts": "New posts", "new_check_ins": "New check-ins", "new_followers": "New followers", "n_p_ur_company": "New people from your company", "n_p_ur_school": "New people from your school", "birthdays": "Birthdays", "pgs_u_m_w_follow": "Pages you may want to follow", "new_meetings": "New meetings", "meeting_updated": "Meeting updated", "meeting_date_time": "Meeting date / time changed", "meeting_cancelled": "Meeting cancelled", "new_messages": "New messages", "meeting_updates": "Meeting updates", "new_highlights": "New highlights", "new_jobs": "New jobs", "new_recommendations": "New recommendations", "new_recommendation_r": "New recommendation request", "recommendation_u_r": "Recommendation update request", "unsubscribed": "Unsubscribed", "unsubscribe": "Unsubscribe", "unsubscribe_msg": "Click on unsubscribe button to stop receiving emails from us on your email address:", "new_world": "A new world is rising. Let us discover it by building your presence with purpose and passion", "social_media": "SOCIAL MEDIA", "social_content": "Do not build links to reach out to people, link with them to build relationships", "job_board": "JOB BOARD", "job_board_content": "Do the best work of your life given once", "job_board_desc": "Great works come with love of what you do. If you look for greatness, let us find what you love", "find_jobs": "Find jobs", "find_jobs_content": "Success is sometimes to do common things in an uncommon way", "follow_industry": "Follow industry", "follow_industry_content": "Know what you own and know what and why you should know", "career_management": "Career management", "career_management_content": "Turn things out to the best making the best of the way things work out", "business_tools": "BUSINESS TOOLS", "business_tools_content": "Do the best to reach the best people", "business_tools_desc": "Gain is mostly not achieved by maximum power, but always by correct tools that will give you maximum", "prime_globe_title": "In a world of clutter and mixed sentiment, use shortcuts to reach your goals", "prime_globe_button": "Reach people", "create_job_p": "Create job posts", "create_job_content": "The best practise in search is to know what you look for and how to call out", "send_tests": "Send tests out", "send_tests_content": "Use your time more productive and let us manage interview process for you", "setup_meetings": "Setup meetings", "setup_meetings_content": "Quit looking for ways to setup a meeting, it is just one click away", "prime_footer_title": "Make your organisation be part of the largest network", "prime_footer_subtitle": "Every fact has its own rules and the most fundamental rule of success for your organisation is to reach further", "prime_footer_button": "Create your business page now", "popular_job_title": "POPULAR JOB CATEGORIES", "popular_job_subtitle": "Choose from the most visited job categories", "finance_title": "Accounting & Finance", "finance_subtitle": "327 Job vacancy", "software_title": "Software Development", "software_subtitle": "168 Job vacancy", "human_resource_title": "Human Resources", "human_resource_subtitle": "214 Job vacancy", "support_title": "Customer Support", "support_subtitle": "138 Job vacancy", "design_title": "UI & UX Designer", "design_subtitle": "206 Job vacancy", "product_title": "Product Management", "product_subtitle": "115 Job vacancy", "marketing_title": "Marketing & Communication", "marketing_subtitle": "415 Job vacancy", "explore_more": "Explore more than", "categories": "categories", "recent_title": "MOST RECENT JOBS APPLIED", "recent_subtitle": "More than 5000+ jobs waiting for you", "easy_career_management_title": "EASY CAREER MANAGEMENT", "easy_career_management_content": "Enhance your job search with our intuitive Dashboard, allowing you to create customized job alerts and manage your profile effortlessly", "benefits_title": "BENEFITS OF A WELL-ORGANIZED PROFILE", "benefits_subtitle": "Complete your profile", "benefits_content": "Did you know that those who fill their profile properly and completely move faster in their careers?", "expand_title": "EXPAND YOUR NETWORK", "expand_subtitle": "Expand your network by making new posts and get one step closer to your dream career", "work_title": "FEEL THE SOCIAL EXPERIENCE AT WORK", "work_subtitle": "Intimate your business with the social life", "work_content": "Be aware of the changes happening around you and open the door for new opportunities", "work_ex_follow_title": "Follow up business posts", "work_ex_follow_content": "More you discover, more you get inspired", "work_ex_inquiry_title": "Let your every inquiry be responded", "work_ex_inquiry_content": "We will urge business entities to resonate with you", "work_ex_power_title": "Use the power of knowledge", "work_ex_power_content": "What you know is who you are", "follow_people_title": "Follow people who work at the company you are interested", "follow_people_content": "Social media is an investment of valuable time and resources. It's important to know what you need and follow people you believe will bring value", "follower": "follower", "follower_cap": "Follower", "followers": "followers", "followers_cap": "Followers", "following": "following", "following_cap": "Following", "mng_by_y": "Managed by you", "followings": "following", "followings_cap": "Following", "professional_experience": "Professional experience", "volunteer_experience": "Volunteer experience", "chance_title": "MAKE EVERY CHANCE POSSIBLE", "chance_subtitle": "You can follow the industry's leading brands and their sectoral posts and at the same time apply for their job openings", "find_audience": "Find audience", "find_audience_content": "Make yourself stand out in the crowd that you feel you have lost", "create_community": "Create a community", "create_community_content": "Build a community with people you care and you are cared by", "find_events": "Find events", "find_events_content": "Be a part of memorable events that make your voice heard", "lobox_motto": "Link outside the box", "lobox_submotto": "to create your own network", "job_opportunities": "Job opportunities", "job_opportunities_content": "Job search has never been easier", "share_articles": "Share articles", "share_articles_content": "The world needs quality information more than ever", "follow_events": "Follow events", "follow_events_content": "Be where you can benefit from many things at once", "read_topics": "Read topics", "read_topics_content": "No need to be here and there to get what you need", "add_profile": "Add profile and cover photo", "add_profile_content": "Adding a profile and a cover photo helps your account to outstand", "complete_sections": "Complete sections", "complete_sections_content": "Complete profiles enables you to be reached out. To show your experience, skills and achievements fill all the sections up", "add_resume": "Add your resume", "r_y_s_y_w_t_del_resume": "Are you sure you want to delete your resume?", "add_resume_content": "Uploading a resume eases recruiters to access your background", "people": "People", "people_notif_setting_msg": "New followers, peoples from your workplace or your school", "people_sm": "people", "pages": "Pages", "pages_sm": "pages", "jobs_lower": "jobs", "jobs": "Jobs", "job": "Job", "req_company": "Enter your company", "req_employmentType": "Enter your contract type", "req_startDate": "Enter date you started working", "education": "Education", "add_education": "Add education", "edit_school": "Edit school", "delete_school": "Delete school", "edit_licence": "Edit licence & certification", "add_licence": "Add licence & certification", "new_licence_certificate": "New licence & certification", "licences_certification": "Licences & Certifications", "delete_licence": "Delete licence & certification", "delete_licence_helper": "Are you sure you want to delete your licence & certification?", "add_publication": "Add publication", "edit_publication": "Edit publication", "delete_publication": "Delete publication", "publications": "Publications", "publication": "Publication", "delete_public_helper": "Are you sure you want to delete your publication?", "add_accomplishment": "Add accomplishment", "awards": "Honor & Awards", "honrs_a_awrs": "Honors & Awards", "add_awards": "Add honor & awards", "edit_awards": "Edit honor & awards", "delete_award": "Delete honor & award", "delete_awards": "Delete honor & awards", "delete_awards_helper": "Are you sure you want to delete your honor & awards?", "patent": "Patent", "patents": "Patents", "edit_patent": "Edit patent", "add_patent": "Add patent", "delete_patent": "Delete patent", "delete_patent_helper": "Are you sure you want to delete your patent?", "see_all_results": "See all results", "add_language": "Add language", "search_language": "Search language", "see_language": "See language", "edit_language": "Edit language", "delete_language": "Delete language", "delete_language_helper": "Are you sure you want to delete your language?", "language_empty_title": "You do not have any languages added to your profile", "language_empty_sub_title": "Adding a new language you can speak helps others know you better", "see_skill": "See skill", "edit_skill": "Edit skill", "add_skill": "Add skill", "add_skills": "Add skill", "search_skill": "Search skill", "delete_skill": "Delete skill", "remove_skill": "Remove skill", "skill_empty_title": "You do not have any skills added to your profile", "skill_empty_sub_title": "Adding a new skill you gain helps others know you better", "hard_skills": "Hard Skills", "soft_skills": "Soft Skills", "other_skills": "Other Skills", "delete_skill_helper": "Are you sure you want to delete your skill?", "write_a_recommendation": "Write recommendation", "write_recommendation": "Write recommendation", "your_recommendation": "Your recommendation", "recommendation": "Recommendation", "recommendations": "Recommendations", "recommendation_n_s_msg": "New recommendations and requests", "delete_recommendation_helper": "Are you sure you want to delete your recommendation?", "delete_resume": "Delete resume", "delete_resume_text": "Are you sure you want to delete your resume?", "cancel": "Cancel", "cancel_lower": "cancel", "upload_new_resume": "Upload new resume", "share_via_message": "Message", "withdraw_app": "Withdraw application", "add_your_resume": "Add your resume", "add_your_resume_helper": "Recruiters can see and download", "download": "Download", "show_all": "Show all", "show_less": "Show less", "delete_experience_helper": "Are you sure you want to delete your experience?", "date_e_b_s": "End date cannot be before start date", "welcome_back_to": "Welcome back to Lobox!", "verification_successful": "Verification successful", "verification_not_successful": "Verification is not successful", "welcome_back_to_message": "Millions of people are building their professional network here. login your account to join them", "limit_100mb_unable_post": "The limit 100MB is exceeded, so unable to post it", "limit_rate_unable_post": "The maximum rate for uploading files exceeded, you can upload 64 files per hour", "limit_100mb_rm_media": "The limit 100MB is exceeded, remove some images or videos", "unable_to_upload_file": "Unable to upload file", "unable_to_upload_files": "Unable to upload files", "check_net_connection": "Check the network connection", "something_went_wrong": "Something went wrong", "something_went_wrong_message": "An error occurred while trying to verify your account", "not_found_profile_page": "This page does not exist", "not_fount_profile_message": "We could not reach the person or page you were looking for. You can check it and try again or you can return to the home page", "send_message": "Send message", "send_via_message": "Send via message", "think_outside_the_box": "Think outside the box!", "done": "Done", "in_progress": "In progress", "write_a_message_3dot": "Write a message", "company_star": "Company", "company_helper": "Example: Lobox", "contract_type_helper": "Full-time contracts", "currently_working_at_this_position": "Currently working at this position", "volunteer": "Volunteer", "start_date": "Start date", "experience_start_date_helper": "Date you started working", "end_date_star": "End date", "experience_end_date_helper": "Date you stopped working", "tell_more_about_experience": "Tell us more about your work experience", "publications_sub_title": "Culture develops with ideas, share your ideas with the world", "award_sub_title": "Share your success with us, let's celebrate together", "patent_sub_title": "Share your discoveries", "write_your_bio_sub_title": "Let all know who you are", "experience_sub_title": "Turn your value to words", "education_sub_title": "Add strength to your value", "accomplishment_sub_title": "Show what you can do", "lt_oth_tel_w_y_r": "Let others tell who you are", "ask_for_a_recommendation_sub_title": "You can ask recommendations from people who you work with", "skills_sub_title": "Share your talents", "language_sut_title": "Open to the outside world", "select_relationship": "Select relationship", "select_relationship_helper": "Example: <PERSON> managed you directly", "your_role_at_the_time": "Your role at the time", "role_at_the_time_star": "Role at the time", "your_role_at_the_time_helper": "Example: UI Designer at Lobox", "your_message_to_person_asking_recommendation": "Your message to the person you are asking for recommendation", "suggestions": "Suggestions", "suggestions_notif_setting_msg": "Suggestions to follow, popular hashtags and new jobs", "title_star": "Title", "course_title_helper": "Example: UX Design, marketing", "institution": "Institution", "provider": "Provider", "institution_helper": "Example: Goethe - Institute", "start_date_star": "Start date", "date_you_started_the_course": "Date you started the course", "end_date": "End date", "date_you_completed_the_course": "Date you completed the course", "tell_us_more_about_your_school": "Tell us more about your education", "share_on_feed": "Share on feed", "share_on_feed_helper": "If checked, your network may be informed", "add_another_website": "Add another website", "edit_contact_details": "Edit contact details", "school_sub_title": "Which university or school did you attend?", "courses": "Courses", "courses_sub_title": "Knowledge is power so share it", "licence_certification": "Licence & Certification", "licence_certification_sub_title": "Share your remarkable strength", "follow_requests": "Follow requests", "follow_requests_sub_title": "Accept or decline requests", "search_follower": "Search", "search_following": "Search", "pending": "Pending", "follow_back": "Follow back", "follow": "follow", "follow_cap": "Follow", "follows": "follows", "required_sm": "required", "required_lg": "Required", "requests": "Requests", "honor_title_helper": "Example: The Pulitzer Prize", "presented_by": "Presented by", "presented_by_helper": "Example: Apple", "issue_date": "Issue date", "honor_issue_date_helper": "Date you get awarded or honored", "link": "Link", "tell_more_about_honor": "Tell us more about your honor & awards", "language_star": "Language", "language_helper": "Example: German, French, Spanish", "level": "Level", "language_level_helper": "Example: A1 (<PERSON><PERSON><PERSON>)", "licence_title_helper": "Example: Product management", "licence_start_date_helper": "Licence acquisition date", "licence_end_date_helper": "Licence expiration date", "tell_more_about_licence": "Tell us more about your licence & certification", "patent_title_helper": "Example: United States Patent Institute", "patent_office_helper": "Example: United States Patent and Trademark Office", "patent_start_date_helper": "Patent obtain date", "patent_or_application_number": "Patent or application number", "tell_more_about_patent": "Tell us more about your patent", "publication_title_helper": "Example: Think Like a UX Researcher", "publisher_helper": "Example: <PERSON><PERSON><PERSON><PERSON>", "publication_issue_date_helper": "Date you issued your publication", "tell_more_about_publication": "Tell us more about your publication", "school_star": "School", "school_title_helper": "Example: Harvard, Yale, MIT", "major": "Major", "major_helper": "Example: Graphic design, Architecture", "degree": "Degree", "degree_star": "Degree", "degree_helper": "Example: Bachelor’s degree", "currently_studying_ath_this_school": "Currently studying at this school", "school_start_date_helper": "Date you started school", "school_end_date_helper": "Leave blank if you have not graduated yet", "skill_title_helper": "Example: <PERSON>, Photoshop, leadership", "skill_level_helper": "Example: Expert", "login_to_follow": "Login to follow", "you_need_to_add_an_experience": "You should least have one experience on your profile to be able to request a recommendation", "not_shown_until_you_accept": "Will not be shown on your profile until you accept", "messaging": "Messaging", "messages": "Messages", "home": "Home", "message": "Message", "company": "Company", "manages": "manages", "highlight": "Highlight", "create_highlight_sub_title": "Share important milestones with your followers", "create_company_sub_title": "Create a company page for communications", "page": "Page", "page_sm": "page", "create_page_sub_title": "Create a page for institutions and public figures", "create_something_amazing": "Create something amazing", "event": "Event", "events": "Events", "create_event_sub_title": "Create a public or private event to bring people together", "article": "Article", "articles": "Articles", "create_article_sub_title": "Share your professional ideas and knowledge with your followers", "group": "Group", "groups": "Groups", "create_group_sub_title": "Create a community and bring people together with the same interests", "topic": "Topic", "create_topic_sub_title": "Start a discussion to get your community's opinion", "uploading_3dot": "Uploading", "file_uploading_3dot": "File uploading", "contact_info": "Contact info", "overview": "Overview", "industry": "Industry", "add_industry": "Add industry", "description": "Description", "add_description": "Add description", "establishment_date": "Establishment date", "add_establishment_date": "Add establishment date", "company_size": "Company size", "page_size": "Page size", "add_company_size": "Add company size", "add_page_size": "Add page size", "search_company": "Search company", "locations": "Locations", "del_location": "Delete location", "del_location_msg": "Are you sure to delete location?", "add_location": "Add location", "search_location": "Search location", "page_title_star": "Page title", "page_title_helper": "Your page name", "category_star": "Category", "page_category_star": "Page category", "industry_star": "Industry", "search_hashtag": "Search hashtag", "phone_number": "Phone number", "add_email": "Add email", "add_phone": "Add phone", "create_page": "Create page", "unpublished": "Unpublished", "unpublished_page": "Unpublished page", "unpublished_pages": "Unpublished pages", "unpublished_page_hint": "This page is only visible to the page admins", "publish": "Publish", "page_details": "Page details", "location_title": "Title of the location", "street_title_star": "Street address", "street_title": "Street address", "state_region": "State / Region", "postal_code": "Postal Code", "primary_location": "Primary location", "primary_location_helper": "Check if this is the central office or headquarter of your business", "tell_more_about_location": "Tell us more about your new location", "edit_address": "Edit address", "delete_address": "Delete address", "page_creation": "Page creation", "create": "Create", "no_contact": "There is no contact to invite", "no_highlight": "There is no highlight yet", "no_highlight_created": "No highlight has been created yet", "no_contact_follow": "There is no contact to follow", "share_via_3dot": "Share via", "edit_page_details": "Edit page details", "publish_page": "Publish page", "publish_page_confirm_helper": "Your page will be visible publicly. Are you sure that you want to publish this page?", "page_title": "Page title", "username": "Username", "page_category": "Page category", "page_created_successfully": "Page created successfully", "page_created_successfully_msg": "You can publish the new page anytime you feel it is ready", "page_published_successfully": "Your page published successfully", "edit_page": "Edit page", "use_lobox_as": "Use Lobox as", "page_role": "Page role", "share_via_post": "Post", "invite": "Invite", "connect": "Connect", "invite_p_t_l": "Invite people to Lobox", "connect_w_p_l": "Connect with people on lobox", "on_lobox": "On Lobox", "company_imgs": "Company images", "invitations_l": "invitations left", "linkedin": "Linkedin", "mutual_following": "mutual following", "invitation_sent": "Invitation sent", "invite_people_title": "Send invitations to your acquaintances to stay in touch with them", "invite_people_sub_title": "You can send invitations through your social media accounts", "sync_invite": "Sync your google account to invite your friends:", "or_send_invite": "Or, send the invitation link to your friends", "you_can_i_m_f": "You can invite more friends at any time", "send_m_inv": "Send more invitations", "done_f_n": "Done for now", "confirm_navigation": "Confirm navigation", "confirm": "Confirm", "leave_title": "Confirm leave", "leave_helper": "Are you sure you want to leave?", "see_all_settings": "See all settings", "success_updated": "successfully updated", "unknown_error": "An issue is happened, try again later", "profile_details": "Profile details", "name_username_l": "Name, username, location, job title, birtday, gender", "profile_user_act": "Profile, user activity", "account_type_delete": "Account type, take a break, delete your account", "security_login": "Security & Login", "password_l_i_d": "Password, logged in devices", "general_settings": "General settings", "lang_notif": "Language, notification", "sub_and_payment": "Subscription and payments", "contact_information": "Contact information", "email_tel_link": "Email addresses, phone number, links", "work_st_citi": "Work status, country of citizenship, resume accessibility", "career_pref": "Career preferences", "site_perf": "Site preferences", "visi_block_user": "Profile visibility, tagged and mentioned content, blocked users, chat status", "mentions_visibility": "Mentions, visibility", "lang_local": "Language & Localization", "sys_lang_time": "Language, date and time format", "feed_pref": "Feed preferences", "content_mention_tag": "Content, mentions, tags, shares, hidden posts", "synchronization": "Synchronization", "linked_acc_cal": "Linked accounts", "notification_settings": "Notification settings", "post_integrations_msg": "New reactions, comments, replies, shares, tags and mentions", "select_t_k_of_notif": "Select the kinds of notifications you want to receive", "lobox_in_other_lng": "Lobox in other languages", "help_support": "Help & Support", "faq_privacy_policy": "Help topics, FAQ, privacy policy, terms of service, about us", "feedback": "<PERSON><PERSON><PERSON>", "contact_us": "Contact us", "about_lobox": "About Lobox", "who_we_are": "Who we are?", "dar_mode": "Dark mode", "turn_on_l": "Turn on the lights", "turn_off_l": "Turn off the lights", "notifications": "Notifications", "go_to_home_p": "Go to home page", "back": "Back", "to_get_t_m_o_lobox_comp_sections": "To get the most out of Lobox, complete these sections as well", "given": "Given", "received": "Received", "no_content_exist": "This content does not exist anymore", "no_content_exist_msg": "We could not reach the content you were looking for. You can check it and try again or you can return to the home page", "feedcard_single_private": "This content is private", "permission_denied": "Access denied", "permission_denied_msg": "You do not have permission to see this content until you get proper access privileges", "feedcard_single_private_msg": "You do not have permission to view this post. To view this post, you must first follow the user", "unhide_post": "Unhide post", "view_page": "View page", "has_issue": "has issue", "public_account": "Public account", "private_account": "Private account", "r_y_s_c_to_pa": "Are you sure you want to change your account to public?", "r_y_s_c_to_pa_page": "Your page will be visible publicly. Are you sure that you want to publish this page?", "r_y_s_c_to_pr": "Are you sure you want to change your account to private?", "published_own": "You’ll need to delete your published pages before deleting your account", "published_own_t": "There are some published pages that you own!", "take_break": "Take a break", "take_break_dot": "take a break", "r_y_s_t_break": "Are you sure you want to take a break from Lobox?", "y_c_reactivate": "You can always reactivate your account with logging in with your existing credentials without loosing any data. While you are on a break we are going to hide your account from search engines and search results", "b_p_w_already_mesg": "But people who already have messages with you might send messages to you and you can get them when you activate your account again", "delete_y_acc": "Delete your account", "r_y_s_delete": "Are you sure you want to delete your account? You’ll lose all your connections, messages, and recommendations", "wrn_undone_lost": "Warning! Deleting you account cannot be undone. You will lost all the data in your account", "we_r_sorry_leave": "We are sorry to see you leave. If you think there is something we can improve you can always contact with us. We would love to hear your opinions", "leave_feedback": "Leave feedback", "delete_account": "Delete account", "if_y_t_y_private": "If you turn your account private, only your followers can see your full profile and posts", "if_y_w_tab": "If you want, you can take a break from Lobox", "y_c_d_y_acc": "You can delete your account permanently. (This cannot be undone!)", "confirm_pass": "Confirm password", "for_y_sec_re_enter": "For your security, re-enter your password to make this change", "business_sm": "business", "business": "Business", "managing": "Managing", "we_h_rec_6_char": "We highly recommend you to choose at least 6 character password with combination of numbers, letters and special characters", "save_new_pass": "Save new password", "passwords_matched": "Passwords matched", "old_new_pass": "New password should not be same as current password", "we_h_rec_6_char_2": "We highly recommend you to choose a strong password with atleast 6 or more characters including letters and numbers", "logged_in_d": "Logged in devices", "log_out_all": "Log out all", "log_out": "Log out", "this_computer": "This computer", "email_addresses": "Email addresses", "to_change_y_c_email_a_f": "To change your current email address on Lobox you should add another one first", "add_another_email": "Add another email address", "waiting_for_verification": "Waiting for verification", "make_primary": "Make primary", "resend": "Resend", "primary": "Primary", "add_a_current_email_to_l": "Adding a current email to your Lobox account can help you log in and receive notifications as well as reset your password easily. It also helps us connect people and others on Lobox", "open_for_j_o": "Open for job opportunities", "if_y_s_job_opp": "If you set your account open for job opportunites even if your account is private, so that recruiters can see your complete profile", "work_status_i_c_l": "Work status in the current living country", "citizen_of_c": "Citizen of the country", "select_your_c_c": "Select your country of citizenship", "select_your_w_s": "Select your work status", "countries_of_citi": "Countries of citizenship", "who_can_down_r": "Who can download your resume?", "hide_this_profile": "Hide this profile on search engines", "if_y_h_y_profile_o_se": "If you hide your profile on search engines, people cannot find your profile outside Lobox. But, your profile will be listed on Lobox search", "review_all_t_p": "Review all the posts that you are tagged and metioned in", "view_all": "View all", "review_all_b_u": "Review all the blocked users", "review_all_b_acc": "Review all the blocked accounts", "edit_visible_p_info_desc": "You control your profile’s appearance for people who are not signed in to Lobox", "edit_visible_p_info": "Edit visible profile information for unregistered visitors", "chat_status": "Chat status", "if_y_c_s_is_o_online": "If your chat status is on, other users can see if you are online. If you turn it off, neither anyone can see your status nor you can see other's status", "a_role_m": "Assign roles to me", "if_y_role": "If your role assignment is on, any page owner can send you a role acceptance request from now. If you turn it off, no one can send you a request for a role anymore", "review": "Review", "rejected_candidate_cant_be_selected": "Rejected candidates can’t be selected", "blocked_accounts": "Blocked accounts", "unblock": "Unblock", "no_tagged_mentioned": "There is not any post that you're tagged or mentioned there", "no_hidden_post": "There is not any post that you have hidden", "unblock_user": "Unblock user", "r_y_s_unblock": "Are you sure you want to unblock?", "warn_if_unb_prev_f": "Warning: If you unblock a user you previously followed, it will not make you follow them again. If you want to follow this user again, you need to send a follow request again", "hide_profile_f_s": "Hide profile from search engines", "r_y_s_hide_p_s": "Are you sure you want to hide your profile from search engines?", "warn_valid_t_proc_wil_dep_index": "Warning: The validity of this process will vary depending on the indexing speed of search engines. We will not allow crawlers to index your profile in the next crawl", "date_format": "Date format", "show_cont_i_fo_tagged": "Show content that I follow, in which people and pages are tagged", "show_cont_i_commented": "Show content that I follow, in which people and pages made a comment", "show_cont_i_reacted": "Show content that I follow, in which people and pages reacted", "allow_other_share_p": "Allow others to share your posts", "allow_other_mention_p": "Allow others to tag or mention you on their posts", "hidden_contents": "Hidden contents", "hidden_posts": "Hidden posts", "linked_accounts": "Linked accounts", "link_another_acc": "Link another account", "link_accounts": "Link accounts", "edit_profile_visibility": "Edit profile visibility", "category": "Category", "published_page": "Published page", "if_y_unp_y_page": "If you unpublish your page, only page members can see your page. Visitors can see your page and not find in search results", "adult_content": "Adult content", "turn_t_f_on_if_adult_content": "Turn this filter on if your page contains some adult content such as alcohol or tobacco products etc", "warn_no_c_nudity_viol": "Warning: No content containing nudity, violence or discrimination of any kind is allowed. Sharing such content may result in permanent deletion of your page", "delete_y_page": "Delete your page", "y_c_d_y_page": "You can delete your page permanently. (This cannot be undone!)", "subscriptions_pay": "Subscriptions & Pay", "membership_pay_meth": "Membership, payment meth", "allow_other_tag_or_me_y_p": "Allow others to tag or mention your page on their posts", "unpublish_page": "Unpublish page", "d_y_w_unpu_page": "Do you want to unpublish your page?", "d_y_w_pu_page": "Do you want to publish your page?", "pub_pages_vis_att": "Published pages are visible to all and anyone can reach your page by search", "unpub_pages_vis_att": "Unpublished pages are only visible to page attendants. Visitors cannot find in search results or reach your page", "r_y_s_d_page": "Are you sure you want to delete your page?", "warn_cn_undo_lost_page": "Warning! This cannot be undone. You will lost all the data and your page url. If you need to hide your page for a while can try to", "unpublish": "Unpublish", "delete_page": "Delete page", "page_username_equal": "Please write the page username", "page_del_confirm": "Delete confirmation", "page_del_confirm_t": "Write page's username and password", "confirm_n_del": "Confirm and delete account", "page_roles": "Page roles", "mgn_page_mem": "Manage page members", "selected_plan": "Selected plan", "use_for_payments": "Use for payments", "add_new_pay_method": "Add a new payment method", "annual": "Annual", "we_al_care_thou_tel_us": "We always care about your thoughts. Tell us about what we can improve", "topic_title": "Topic title", "tell_us_more_star": "Tell us more", "show_comment": "Show comment", "hide_comment": "<PERSON><PERSON> comment", "delete_comment": "Deleted comment", "report_comment": "Report comment", "edit_comment": "Edit comment", "copy_comment_link": "Copy comment link", "comment_is_hidden": "This comment is hidden", "type_name_email": "Type name or email", "admin_have_ful_c_post": "Remember admins have full authority and can even remove you off the page", "editor_c_p_post_r_comm": "Editors can publish posts and articles, organize events, add and remove comments, also send messages as well as follow and unfollow other people and pages", "recruiters_c_c_job_post": "Recruiters can create job posts, send tests and organize meetings with candidates as well as they can view job applications, send messages, and manage all the steps in the recruitment process of the page", "experts_c_c_test_m_elevation": "Experts can create tests and make their evaluation and assessment as they are entitled for those particular tests. Besides, they collaborate with recuiters when they need expertise to create and review a test", "remove_t_role": "Remove this role", "remove_page_role": "Remove page role", "page_role_unblock": "You have already blocked this user. In order to assign a role to this user, you must first unblock it", "r_y_s_w_r_role": "Are you sure that you want to remove this role?", "review_content": "Review content", "about_page_roles": "About page roles", "admin": "Admin", "editor": "Editor", "recruiter": "Rec<PERSON>er", "expert": "Expert", "select_all": "Select all", "enter_y_mail_reg_lob": "Enter your email address registered in Lobox", "new_password": "New password", "sys_lang": "System language", "select_date_format": "Select date format", "current_password": "Current password", "password": "Password", "star_required_fields": "Required fields", "add_another_one": "Add another one", "country_of_citiz": "Country of citizenship", "select_y_c_o_c": "Select your country of citizenship", "photo": "Photo", "video": "Video", "checkin": "Check-in", "checkin_desc": "You can tag only geographical locations.Businesses and places are not supported", "empty_block_msg": "You do not have any blocked user or page", "empty_msg": "No result to show", "add_an_exp": "Add an experience", "add_an_exp_desc": "In order to create a promotion highlight, you need to have an experience for a role that you are currently working in", "confirm_title": "Discard changes", "confirm_desc": "Are you sure you want to discard changes you made?", "confirm_ok": "Continue editing", "confirm_cancel": "Discard", "discard": "Discard", "post_add_success": "Your post shared on your feed", "post_update_success": "Post updated successfully", "successfully": "successfully", "create_post_title": "Create post", "edit_media_title": "Edit media", "post_richtext_ph": "Think outside the box!", "post_add_m_t": "Add photo or video", "tag_person": "Tag person", "tag_profiles": "Tag profiles", "person_sm": "person", "edit_location": "Edit location", "edit_highlight": "Edit highlight", "add_highlight": "Add highlight", "upload_error": "File upload error", "try_again": "Try again", "add_new": "Add new", "highlight_title": "Share important moments", "upload_connection_err": "We encountered an error. check your internet connection and try again", "publish_on_profile": "Publish on your profile", "publish_on_profile_desc": "This highlight is going to be added to your profile", "highlight_desc": "Tell us more about your story", "example": "Example", "create_highlight": "Create highlight", "work": "Work", "life_style": "Life Style", "achivement": "Achievement", "self_development": "Self Development", "new_highlight": "New highlight", "highlight_s_t_hint": "Swimming, running, boxing", "new_job": "New job", "new_job_desc": "Share your new experience", "contract_type": "Contract type", "get_promoted": "Get promoted", "get_promoted_desc": "Your effort is paid off", "entrepreneurship": "Entrepreneurship", "entrepreneurship_desc": "Share your courage", "new_school": "New school", "new_school_desc": "Share your development", "new_course": "New course", "new_course_hint": "Cherish your evolvement", "new_graduation": "New graduation", "new_graduation_desc": "Share your excitement", "study_abroad": "Study abroad", "study_abroad_desc": "Show the sky is limit", "new_travel": "New travel", "new_travel_desc": "Share your discoveries", "to_where": "To where", "from_where": "From where", "new_instrument": "New instrument", "new_instrument_desc": "Show your capabilities", "which_instrument": "Instrument name", "which_instrument_desc": "Guitar, piano, drums", "new_hobby": "New hobby", "new_hobby_desc": "New enjoyment empowers", "new_hobby_desc_more": "<PERSON><PERSON>, painting, origami", "new_sport": "New sport", "new_sport_desc": "Share your new action", "achievement": "Achievement", "new_licence": "New licence", "new_licence_desc": "Show you unlocked things", "driver_licence": "Driver license", "get_funded": "Get funded", "get_funded_desc": "You overcame obstacles", "get_funded_h_desc": "Share the greatness of your company", "amount": "Amount", "amount_star": "Amount", "new_patent": "New patent", "new_patent_desc": "First step for change", "Li_Ion_battery": "Li-Ion battery", "patent_office": "Patent office", "patent_office_star": "Patent office", "new_publication": "New publication", "new_publication_desc": "Share ideas for development", "t_l_a_ux_res": "Think like a UX researcher", "publisher": "Publisher", "new_language": "New language", "new_language_desc": "Share all what you can speak", "new_award": "New award or achievement", "new_awards_a_achivs": "New awards or achievements", "new_award_desc": "Show what you are able to do", "The_p_prize": "The Pulitzer Prize", "new_certification": "New certification", "product_management": "Product management", "new_skill": "New skill", "new_skill_desc": "Share your new talents", "new_adventure": "New adventure", "new_adventure_desc": "Share your excitement", "adventure_desc_more": "Climbing Mount Everest", "conference_participation": "Conference participation", "conference_participation_desc": "Share personal development", "conference_more_desc": "International Conference on Global Business, Economics, Finance and Social Sciences", "email": "Email", "in_app": "In-app", "desktop": "Desktop", "verify_phone_number": "Verify phone number", "w_w_send_verify_code_t": "We will send a verification code to", "with_sms_is_correct": "via SMS. Is this number correct?", "w_h_s_6dig_verify_code_phone_sms": "We have sent a 6-digit verification code to your phone via SMS. enter the code from the SMS to verify your phone number", "enter_v_code": "Enter verification code", "did_not_r_sms": "Did not recieve the SMS?", "log_out_f_a_d": "Log out from all devices", "d_y_w_logout_this": "Do you want to log out this device?", "d_y_w_log_out_all_d": "Do you want to log out from all logged-in devices?", "no": "No", "discard_changes": "Discard changes?", "edit": "Edit", "click_to_add_work_status": "Click to add work status", "click_to_add": "Click to add", "click_to_add_d": "Click to add date format", "click_to_add_l": "Click to add language", "click_to_add_c_o_c": "Click to add country of citizenship", "push": "<PERSON><PERSON>", "specify": "Specify", "no_result_to_show": "No result to show", "verify": "Verify", "page_name_username_l": "Name, username, category, description", "page_account_type_delete": "Page status, adult content, delete page", "page_mgn_page_mem": "Manage page members and roles", "page_email_tel_link": "Email address, phone number, links, location", "page_visi_block_user": "Review tagged content, blocked users", "page_content_mention_tag": "Content, mentions, tags, shares, hidden posts", "page_membership_pay_meth": "Current plan, payment methods", "page_notification_settings": "Select the kinds of notifications you want to receive", "page_help_support": "Help topics, FAQ, privacy policy, terms of service, about us", "promotion_h_title": "Select the role you were promoted to", "promotion_h_action": "Add another experience", "new_job_list_title_single": "Do you want to end this position?", "new_job_list_title_multi": "Do you want to end any of these position?", "expension": "Expansion", "change": "Change", "evolvement": "Evolvement", "celebration": "Celebration", "get_aquired": "Get acquired", "get_aquired_desc": "Share your new assets with your followers", "aquired_name": "Acquired asset name", "new_location": "New location", "new_location_desc": "Share your new location with the world", "at_where": "At where", "changed_name": "Changed name", "changed_name_desc": "Share your page's new name with your followers", "changed_name_input": "New name", "market_value": "Market value", "market_value_desc": "Share the magnificence of your company with the world", "current_value": "Current value", "previous_value": "Previous value", "stock_value_change": "Stock value change", "stock_value_change_desc": "Share your company’s value gain of stock", "current_stock_value": "Current stock value", "previous_stock_value": "Previous stock value", "get_invested": "Get invested", "get_invested_desc": "Share new investors with your company’s followers", "investod_name_input": "Investor name", "going_public": "Going public (IPO)", "going_public_desc": "Share the value of your company", "initial_stock_value": "Initial stock value", "anniversary": "Anniversary", "anniversary_desc": "Share the important moments of the company", "establishment_anniversary": "Establishment anniversary", "new_team_member": "New team member", "new_team_member_desc": "Share the new player of your team with people", "new_member_position": "New member role", "area_of_interest_star": "Area of interest", "area_of_interest": "Area of interest", "add_area_of_interest": "add area of interest", "institution_size": "Institution size", "add_inst_size": "Add institution size", "organization_size": "Organization size", "add_organization_size": "Add organization size", "richtext_emoji_tooltip": "Add emoji", "req_phone": "Enter your phone number", "phone_not_valid": "Phone number is not valid", "SUICIDE_OR_SELF_HARM": "Suicide or self harm", "VIOLENCE": "Violence", "TERRORISM": "Terrorism", "FALSE_NEWS": "False news", "NUDITY": "Nudity", "HARASSMENT": "Harassment", "SPAM": "Spam", "HATE_SPEECH": "Hate speech", "select_conv_crt_str_txt": "Select a conversation or create a new one", "t_on_active_st": "Turn on active status", "msg_reqs": "Message requests", "archived_msgs": "Archived messages", "blck_users": "Blocked users", "search_in_convs": "Search in conversation", "archive": "Archive", "archived": "Archived", "mute": "Mute", "block_msg": "Block message", "mark_as_read": "<PERSON> as read", "group_info": "Group info", "add_cover_img": "Add cover image", "created": "Created", "at": "at", "is_at": "is at", "media": "Media", "documents": "Documents", "starred_messages": "Starred messages", "participants_cap": "Participants", "participants": "participants", "add_participant": "Add participant", "remove_admin": "Remove admin", "mark_as_admin": "<PERSON> as admin", "remove_from_group": "Remove from group", "remove_f_followers": "Remove from followers", "show_info": "Show info", "hide_info": "Hide info", "replied": "Replied", "forward": "Forward", "happy_birthday": "Happy Birthday", "is_asking_for_a_recom": "is asking for a recommendation", "dismiss": "<PERSON><PERSON><PERSON>", "go_to_primary": "Go to primary", "oh_its_empty_here": "Oh! it’s quite empty here", "deleted": "Deleted", "blocked": "Blocked", "unBlocked": "Unblocked", "typing_3dot": "Typing", "undo": "Undo", "media_tag_link": "Media, document and links", "no_media_show": "No media to show", "no_doc_show": "No document to show", "no_link_show": "No links to show", "reply": "Reply", "replying_to": "Replying to", "reply_lower": "reply", "forward_message": "Forward message", "star_message": "Star message", "delete_message": "Delete message", "forwarded": "Forwarded", "no_stared_msg_show": "No starred message to show", "mov_all_primary": "Move all to primary", "del_all_msg": "Delete all messages", "block_all": "Block all", "unblock_all_users": "Unblock all users", "write_a_message": "Write a message", "r_y_s_t_w_m_al_primary": "Are you sure that you want to move all messages to primary?", "r_y_s_t_w_del_al_msgs": "Are you sure that you want to delete all messages?", "r_y_s_t_w_block_al_msgs": "Are you sure that you want to block all messages?", "r_y_s_y_w_t_remove": "Are you sure you want to remove", "f_the_g_q": "from the group?", "as_admin_q": "as admin?", "will_h_s_p_a_r_u_s_q": "will have the same powers as you, are you sure?", "last_seen": "Lastseen", "add_people_t_s_mes": "Add people to start messaging", "create_g_t_s_mes": "Create group to start messaging", "y_invite": "You invite", "invite_sent": "Invitation sent", "invite_error": "Invitation has ended up an issue", "remains": "<PERSON><PERSON><PERSON>", "people_to_lobox": "{'': 'people to Lobox.'}", "move_to_primary": "Move to primary", "r_y_s_y_w_t_unf": "Are you sure you want to unfollow?", "shared": "Shared", "switch_to_business": "Switch to business", "type_star": "Type", "page_type_star": "Page type", "add_category": "Add category", "search_category": "Search category", "type": "Type", "page_type": "Page type", "recom_req_sent": "Recommendation request sent", "revision_req_sent": "Revision request sent", "review_pen_req_b_y": "Review pending requests sent by you", "y_r_n_f_anyone": "You are not following anyone", "highlight_simple": "New highlight", "h_simple_shared": "had a new highlight", "highlight_job": "Started a new job", "h_job_shared": "has started a new job", "highlight_get_promoted": "Get promoted", "h_get_promoted_shared": "got promoted", "highlight_entrepreneurship": "Entrepreneurship", "h_enterpreneurship_shared": "became an Entrepreneur", "highlight_school": "New school", "h_school_shared": "has started a new school", "highlight_course": "New course", "h_course_shared": "has started new course", "highlight_graduation": "New graduation", "h_graduation_shared": "graduated from", "highlight_study_abroad": "Study abroad", "h_study_abroad_shared": "went to study abroad", "highlight_travel": "New travel", "h_travel_shared": "has started a new travel", "highlight_instrument": "New instrument", "h_instrument_shared": "started playing a new instrument", "highlight_hobby": "New hobby", "h_hobby_shared": "had a new hobby", "highlight_sport": "New sport", "h_sport_shared": "has started a sport", "highlight_license": "New license", "h_license_shared": "got a new license", "highlight_get_funded": "Get funded", "h_get_funded_shared": "got funded", "highlight_patent": "New patent", "h_patent_shared": "obtained a new patent", "highlight_publication": "New publication", "h_publication_shared": "released a new publication", "highlight_language": "New language", "h_language_shared": "has started learning a new language", "highlight_award": "New award or achievement", "h_award_shared": "got a new award", "highlight_certification": "New certification", "h_certification_shared": "got a new certification", "highlight_skill": "New skill", "h_skill_shared": "gained a new skill", "highlight_adventure": "New adventure", "h_adventure_shared": "went on a new adventure", "highlight_conference_participation": "Conference participation", "h_conference_participation_shared": "participated a conference", "highlight_acquired": "Get acquired", "h_acquired_shared": "get acquired", "highlight_new_location": "New location", "h_new_location_shared": "has a new location", "highlight_changed_name": "Changed name", "h_changed_name_shared": "has a new name", "highlight_market_value": "New market value", "h_market_value_shared": "has a new market value", "highlight_stock_value_change": "New stock value", "h_stock_value_change_shared": "has a new stock value", "highlight_get_invested": "Get invested", "h_get_invested_shared": "has a new investor", "highlight_ipo": "Going public (IPO)", "h_ipo_shared": "became public", "highlight_anniversary": "Anniversary", "h_anniversary_shared": "has a new anniversary", "highlight_new_team_member": "New team member", "h_new_team_member_shared": "has a new team member", "y_h_n_followers": "You do not have any followers", "y_h_n_follow_req": "You do not have any follow request", "an_er_oc_w_verify_y_a": "An error occurred while trying to verify your account", "re_verify_code": "Resend verification code", "verify_time_h_ex": "Verification time has expired", "no_email_to_show": "No email address to show", "no_phone_to_show": "No phone number to show", "no_website_to_show": "No website to show", "add_website": "Add website", "to_write_recom_f_sommeone_they_have_one_exper": "Writing a recommendation requires user to have at least one experience on profile therefore you cannot fulfill this at the moment", "close": "Close", "to_write_recom_f_sommeone_you_have_one_exper": "To write a recommendation for someone, you must have at least one experience", "to_write_recom_f_sommeone_both_have_one_exper": "To write a recommendation for someone, you both must have at least one experience", "has_n_followers": "has no followers", "does_n_f_any": "does not follow anyone", "is_n_f_b_any": "is not followed by anyone", "your_message_to_recommended_p": "Your message to the recommended person", "warning": "Warning", "to_ask_recom_f_s_t_m_h_one_exp": "To ask a recommendation from someone, they must have at least one experience", "you": "you", "you_cap": "You", "w_rec_y_rep": "We received your report", "our_t_w_rev_l_y_result": "Our team will review it shortly and let you know the result", "no_recent_searches": "No recent search", "search_for": "Search for", "w_could_n_f_a_result": "We could not find any result", "try_refining_search": "Try refining your search or filters", "make_s_y_pel_key_looking_f_c_another_key": "Make sure the keyword you entered is spelled correctly or try another one", "nothing_found": "Nothing found", "no_u_found": "No user found", "r_y_s_y_w_del_msg": "Are you sure you want to delete this message?", "delete_for_every_one": "Delete for everyone", "delete_for_me": "Delete for me", "plz_ent_n_val": "Enter numeric value", "to_may_login_att_p_t_again": "Too many login attempts, try again 1 hour later", "reset_pass_error": "Reset password error", "to_may_reset_pass_att_p_t_again": "Too many reset password attempts, try again 1 hour latter", "y_blocked_t_acc": "You blocked this account", "i_y_t_t_mistake_g_s": "If you think this occurred by mistake, go to “settings” to revert it", "r_y_s_block": "Are you sure you want to block?", "new_req": "New requests", "new_follow_req": "New follow requests", "upcoming_birthdays": "Upcoming birthdays", "celebrate": "Celebrate", "no_upcom_birthd_f": "No upcoming birthday found", "people_y_mk": "People you may know", "popular_hashtags": "Popular hashtags", "no_follow_req_f": "No follow request found", "popular_pages": "Popular pages", "no_p_found": "No page found", "pages_t_follow": "Pages to follow", "select_one_of_sug_cate": "Select one of the suggested categories", "y_r_blocked": "You are blocked", "y_r_blocked_by_t_acc": "You are blocked by this account", "select_one_of_sug_cites": "Select one of the suggested cites", "select_one_of_sug_address": "Select one of the suggested addresses", "select_one_of_sug_countries": "Select one of the suggested countries", "select_one_of_sug_users": "Select one of the suggested users", "select_one_of_sug_langs": "Select one of the suggested languages", "select_one_of_sug_skls": "Select one of the suggested skills", "select_one_of_sug_currencies": "Select one of the suggested currencies", "city-district": "City / District", "add_companies": "Add company", "add_schools": "Add school", "search_school": "Search school", "write_a_j_title": "Write a job title", "filter": "Filter", "apply": "Apply", "has_job_offers": "Has job offers", "search_filter": "Search filter", "country": "Country", "add_categories": "Add category", "add_industries": "Add industry", "add_dates": "Add date", "add_date": "Add date", "posts_by_me": "Posts created by you", "post_date_span": "Post date span", "starting_date": "Starting date", "choose_a_source": "Choose a source", "hashtag_date_span": "Hashtag date span", "following_hashtags": "Following hashtags", "post_from": "Posts from", "you_empty_received_recom": "You do not have any received recommendation", "you_empty_given_recom": "You do not have any given recommendation", "find_people": "Find people", "peoples": "People", "w_c_n_r_hashtag_empty": "We could not find any posts created attached with this hashtag", "t_hashtag_not": "This hashtag does not exist", "u_invite_more": "You can invite more friends at any time", "s_m_inv": "Send more invitations", "done_now": "Done now", "existed_fail_msg": "invitation failed, because the user is already existing in Lobox:", "existed_fails_msg": "invitations failed, because the users are already existing in Lobox:", "enter_g_title": "Enter group title", "room_created_sucss": "Room created successfully", "r_y_s_y_w_t_delete": "Are you sure you want to delete?", "delete_group": "Delete group", "room_deleted_sucss": "Room deleted successfully", "clear_history": "Clear history", "r_y_s_y_w_clear_history": "Are you sure you want to clear history of this conversation?", "edit_message": "Edit message", "if_y_t_t_o_b_m": "If you think this occurred by mistake, go to", "t_revert_it": "to revert it", "request_sent": "Request sent", "request_declined": "Request declined", "followed_by": "Followed by", "followed_by_me": "Followed by you", "followed_by_y": "Followed by you", "svd_b_y": "Saved by you", "svd_b": "Saved by", "mentions_on_comment": "Mentions on comment", "no_email_ent": "No email entered", "no_desc_ent": "No description entered", "no_location_ent": "No location entered", "no_p_number_ent": "No phone entered", "no_web_ent": "No website entered", "i_v_t_i_auth_repr_org_h_right": "I verify that I am an authorized representative of this organization and have the right to act on its behalf in the creation and management of this page. The organization and I agree to the additional", "for_pages_dot": "for pages", "terms_sm": "terms", "selected_filters": "Selected filters", "posted_by_me": "Posted by me", "this_acc_is_private": "This account is private", "this_acc_is_private_sub": "Follow this account to expand your network and know more about it", "follow_se_p_cont": "Follow to see profile content", "ent_category": "Entry category", "ent_company": "Enter company", "ent_school": "Enter school", "enter_j_title": "Enter job title", "reset_pass_err_title": "Reset password error", "reset_pass_err_sub": "An error occurred while trying to reset your password", "add_company": "Add company", "add_j_title": "Add job title", "search_j_title": "Search job title", "mark_as_unread": "<PERSON> as unread", "mark_as_read_msg": "Mark this notification read", "turn_off": "Turn off", "mark_read": "Mark all as read", "turn_off_notif_msg": "Stop recieving notification like this", "delete_notif_msg": "Delete this notification", "leave": "Leave", "this_acc_blocked": "This account is blocked", "tis_msg_h_b_del": "This message has been deleted", "this_device": "This device", "edit_cover_img": "Edit cover image", "unstar_msg": "Unstar message", "stared_messages": "Stared messages", "every_one": "Everyone", "my_followers": "My followers", "only_me": "Only me", "delete_phone": "Delete phone number", "h_market_value_t_prefix": "Current value", "h_market_value_sub_prefix": "Previous value", "h_stock_value_change_t_prefix": "Current stock value", "h_stock_value_change_sub_prefix": "Previous stock value", "h_get_invested_sub_prefix": "Amount", "h_get_funded_sub_prefix": "Amount", "h_ipo_t_prefix": "Initial stock value", "send_file": "Send file", "send_image": "Send image", "leave_g": "Leave group", "y_r_o_admin_g_else_leave": "You are the only admin of the group. You should make someone else admin before leaving the admin role", "find_people_follow": "Find people to follow", "es_home_desc": "Click the button below to see whom you can follow to expand your network and make new professional connections. Filling in your profile sections makes it easier for us to help you reach to millions", "click_to_add_location": "Click to add location", "everyone_at_lobox": "Everyone at Lobox", "post_not_found": "Post not found", "profile_not_found": "Profile not found", "click_here_f_g_in": "Click here for group info", "owner": "Owner", "you_asked_a_recomm_from": "You asked a recommendation from", "asked_recomm_from_y": "asked recommendation from you", "recommendation_not_found": "Recommendation not found", "created_this_room": "created this room", "post_shared_succ": "Post sent via message", "user_profile_sh_succ": "User profile sent via message", "page_sh_succ": "Page sent via message", "can_n_send_msg_to_blocked_acc": "Cannot send a message to blocked contact", "you_write_a_recomm_for": "You write a recommendation for", "gives_you_a_recomm": "gives you a recommendation", "resume_shared_succ": "Resume shared successfully", "asked_you_a_recom": "asked you a recommendation", "could_n_f_p_msg": "Could not fetch previous messages", "forwarded_msg": "Forwarded message", "replied_msg": "Replied message", "pin_msg": "Pin message", "pinned_msg": "Pinned message", "mark_all_as_read": "Mark all as read", "empty_pending_recommendation": "You do not have any pending request", "y_sent_a_recom_to": "You sent a recommendation to", "sent_y_a_recom": "sent you a recommendation", "asked_you_a_revision": "asked for revising from you", "you_asked_a_revision_from": "You asked a revise from", "requested": "Requested", "hi": "Hi", "c_y_p_revise_y_recom_f_spec": "Can you revise your recommendation?", "press_es_t": "Press Esc to", "write_a_r": "Write a reply", "writ_a_c": "Write a comment", "see_m_comments": "see more comments", "make_comment": "Make comment", "more_replies": "More replies", "replies_lower": "replies", "replies": "Replies", "there_i_n_comm_y": "There is no comment made yet", "add_y_comm_t_b_f": "Add your comment to be the first", "search_f_m_w": "Search for messages with {user_first_name_lastname}", "today_cap": "Today", "confirm_req": "Confirm request", "delete_req": "Delete request", "sent_y_a_f_r": "sent you a follow request", "this_sec_interactions_posts": "This section depicts the numbers of interactions viewers have committed to your posts", "this_sec_dis_view_posts": "This section displays the list of your posts viewers have mostly interacted", "connecting_3dot": "Connecting", "computer_not_conn": "Computer not connected", "mke_su_y_com_ac_internet": "Make sure your computer has an active Internet connection", "share_on_h": "Share on home", "write_a_p": "Write a post", "share_t_page": "Share on page", "del_f_everyone": "Delete for everyone", "also_del_for": "Also delete for", "un_mute": "Unmute", "published_a_n_p": "published a new page", "view_post": "View post", "there_r_n_unread_notif": "There are no unread notifications to show", "there_r_n_notif_t_sho": "There are no notifications to show", "t_hash_d_n_cont": "No content related to this hashtag is found", "notification_center": "Notification center", "mend_y_o_cmt": "mentioned you on a comment", "req_y_t_b": "requested you to be", "acc_role_r": "accepted role request", "dec_role_rea": "declined role request", "started_f_y": "started following you", "has_bir_tod_send_cel_msg": "has birthday today! Send a celebration message", "acc_y_f_re": "accepted your follow request", "wan_t_f_y": "wants to follow you", "rep_y_comm": "replied your comment", "react_yr_comm": "found your comment not relevant", "react_to_yr": "found your post not relevant", "requested_y_t_writ_doYo": "requested you to write a recommendation. Do you want to write now?", "post_shared_o_f": "Post shared on feed", "link_copied_to_clipboard": "Link copied to clipboard", "post_shared_o_p": "Post shared on page", "new_login": "New login", "a_nw_dv_log_i_f": "A new device logged in from", "ds_is_nt_y_rw_l_d": "This is not you? Review logged devices", "review_devs": "Review devices", "default": "<PERSON><PERSON><PERSON>", "unread": "Unread", "mark_all_a_r": "Mark all as read", "open": "Open", "open_lower": "open", "closed": "Closed", "Unpublished": "Unpublished", "create_job": "Create job", "rec_app_by_lobox": "Receive applicants by Lobox", "job_title_star": "Job title", "job_type_star": "Job type", "job_type": "Job type", "exp_level": "Experience level", "rec_app_by": "Receive applicants by", "details": "Details", "edit_job": "Edit job", "previous": "Previous", "job_add_success": "Your job created successfully", "no_salary_rn_ent": "No salary range entered", "delete_job": "Delete job", "edit_lng": "Edit language", "delete_job_helper": "Are you sure you want to delete your job?", "publish_job": "Publish job", "publish_job_confirm_helper": "Your job will be visible publicly. Are you sure that you want to publish this job?", "job_published_successfully": "Your job published successfully", "unpublished_job": "Unpublished job", "unpublished_job_hint": "This job is only visible to the job owner", "empty": "Empty", "rec_search_i_empt_di_wor": "Recent search is empty, start to discover new jobs all around the world", "applied_lower": "applied", "hired_lower": "hired", "applied": "Applied", "hired": "<PERSON><PERSON>", "closed_lower": "closed", "active_lower": "active", "saved": "Saved", "open_job": "open job", "unpublished_job_lower": "unpublished job", "applicants": "Applicants", "status": "Status", "closed_job": "closed job", "archived_job": "archived job", "crte_jb_add_tl_peop": "Create jobs to add the most talented people", "no_job_found": "No open jobs found", "found": "found", "no_skill_ent": "No skill entered", "no_lng_ent": "No language entered", "add_lngs": "Add language", "track": "Track", "write_question": "Write question", "question_type": "Question type", "question": "Question", "answer": "Answer", "selections": "Selections", "choices": "Choices", "option": "Option", "add_option": "Add option", "allow_multiple_selections": "Allow multiple selections", "must_have_skl": "Must-have this skill", "add_hashtag": "Add hashtag", "save_job": "Save job", "unsave_job": "Unsave job", "set_alert": "Set alert", "application_set_succ": "Application sent successfully", "preview": "Preview", "choose_lng": "Choose language", "employ_work_o_s": "people work on-site", "employ_work_o_s_off": "people work on-site and remote", "employ_work_off": "people work remote", "employ": "people", "hybrid": "Hybrid", "on_site": "On-site", "add_job_type": "Add job type", "no_job_ty_en": "No job type entered", "no_job_model_en": "No job model entered", "no_cate_entr": "No category entered", "add_exp_level": "Add experience level", "search_exp_level": "Search experience level", "no_exp_lev_ent": "No experience level entered", "add_sal_rng": "Add salary range", "discover_jobs": "Discover jobs", "applied_jobs": "Applied jobs", "saved_jobs": "Saved jobs", "withdraw": "Withdraw", "withdraw_the_job": "Withdraw the job", "r_y_s_witd_job": "Are you sure you want to withdraw your application?", "withdraw_job": "Withdraw job", "job_withdrawn": "Job withdrawn", "success_withdrawn": "job alert is successfully withdrawn", "edit_owners": "Edit owners", "owners": "Owners", "cpy_link": "Copy link", "cpy_post_link": "Copy post link", "cpy_job_lnk_msg": "Link copied", "cpy_link_msg": "The job link you copied is ready for sharing", "shar_via_twit": "Share via Twitter", "shar_via_face": "Share via Facebook", "shar_via_linkdin": "Share via Linkedin", "jobs_found": "jobs found", "edit_job_details": "Edit job details", "add_as_owner": "Add as owner", "remove_owner": "Remove owner", "owner_removed": "Owner removed", "owner_added": "Owner added", "add_owner": "Add owner", "search_colle_3dot": "Search colleagues", "r_y_s_w_remove": "Are you sure that you want to remove", "f_t_owner_lst": "from the owner list?", "r_y_s_w_add": "Are you sure that you want to add", "as_owner_q": "as owner?", "is_with": "is with", "in": "in", "company_info": "Company info", "meet_hire": "Meet the associate", "name_looking_new_member": "<b>{name}</b> is looking for a new team member!", "looking_new_member": "is looking for a new team member!", "apply_now": "Apply now!", "job_applied": "Job applied", "view_applied_jbs": "View applied jobs", "view_saved_jbs": "View saved jobs", "job_app_is_success": "job application is successfully applied", "popular_jobs": "Popular jobs", "confirm_unsave_question_1": "Are you sure that you want to remove", "confirm_unsave_question_2": "job from your saved jobs?", "confirm_save_question_1": "Are you sure that you want to save", "confirm_save_question_2": "job?", "toast_unsave_job": "job is removed from your saved jobs", "toast_save_job": "job is saved to your saved jobs", "job_saved": "Job saved", "job_unsaved": "Job unsaved", "add_members_who_ad_own": "Add member who you want to be owner", "success_rem_f_ow_lis": "successfully removed from the owner list", "success_added_f_ow_lis": "successfully added to the owner list", "type_mem_name": "Type member name", "share_to_page": "Share on page", "no_result_f_y_pg": "No result found in your pages", "no_result_f": "No results found", "no_result_found": "No results found", "file_types_accept": "Accepted file types: .doc, .docx and .pdf only", "drg_drp_resume_title": "Drag & drop your resume here or", "upload_your_resume": "to upload your resume", "upload_resume": "upload resume", "click_here": "Click here", "remove_all": "Remove all", "remove_all_recent_jobs_lbl": "Remove all recent search", "remove_recent_search_lbl": "Remove recent searches", "remove_all_recent_msg": "Are you sure that you want to remove all the recent search?", "remove_recent_msg_first": "Are you sure that you want to remove", "remove_recent_msg_second": "from your recent search?", "remove_recent_msg": "search record removed from recent search list", "remove_all_recent_toast_lbl": "All search deleted", "remove_recent_toast_lbl": "Search removed", "remove_all_recent_toast_msg": "All recent search records deleted from recent search list", "remove_search": "Remove search", "yor_profile_updated_suces": "Your profile updated successfully", "share_t_p": "Share on page", "y_c_search_th_j_id": "You can search the job via this ID", "add_recipients": "Add recipients", "group_chats": "Group chats", "edit_recipients": "Edit recipients", "mutual_followers": "Mutual followers", "hashtag_tooltip": "Adding hashtag increases the chance of your job being seen more to users", "add_ext_link": "Add external job link", "web_address_star": "Website address", "sort_by": "Sort by", "job_model": "Job model", "date_posted": "Date posted", "external_job": "External job", "most_recent": "Most recent", "mos_relevant": "Most relevant", "j_title": "Job title", "j_type": "Job type", "add_field": "Add field", "on_s_rem": "On-site/Remote", "show_results": "Show results", "page_per_company": "Page (Company)", "benefits": "Benefits", "all_filters": "All filters", "salary_rng": "Salary range", "salary_period": "Salary period", "currency": "<PERSON><PERSON><PERSON><PERSON>", "reset": "Reset", "min": "Min", "max": "Max", "search_jobs": "Search", "schedules": "Schedules", "works_here": "Works here", "no_result_f_wi_filters": "No result found with the current filtering", "no_jobs_filter_f_wi_entry": "No job found with the current filters", "no_all_f_wi_entry": "No result found", "no_hashtags_f_wi_entry": "No hashtag found", "no_posts_f_wi_entry": "No post found", "no_people_f_wi_entry": "No people found", "no_job_f_wi_entry": "No job found", "no_pages_f_wi_entry": "No page found", "apply_at_lobox": "Apply at Lobox", "job_owners": "Job owners", "overview_star": "Overview", "location_star": "Location", "details_star": "Details", "use_template": "Use template", "or": "Or", "min_salary": "Minimum salary", "max_salary": "Maximum salary", "phone_number_star": "Phone number", "cover_letter": "Cover letter", "cover_letter_star": "Cover letter", "upload_date": "Upload date", "personal_info": "Personal information", "questions": "Questions", "review_ur_app": "Review your application", "terms_of_jobs": "terms of jobs", "see_details": "View", "job_updated_success": "Your job updated successfully", "created_by": "Created by", "hosted_by": "Hosted by", "mutuals": "Mutuals", "mutual": "Mutual", "w_r_go_r_closed": "We are going to remove closed jobs after 1 week", "did_y_apply": "Did you apply?", "y_c_t_y_application": "You can track your application in your applied jobs", "is_something_w_job": "Is something wrong with this job?", "please_s_y_r_report": "Please send your reason for reporting this job", "added_t_y_external": "Added to your external applied jobs", "has_been_blocked": "has been blocked", "view_settings": "View settings", "external_job_ref_helper_txt": "External jobs refer to job opportunities outside of your current organization in Lobox", "no_followers": "No followers", "doesnt_h_a_f_f_t_keep": "does not have any follower yet. Follow them to keep up with their activity on the platform", "no_following": "No following", "isnt_f_an_y_ch_lat": "isn't following anyone yet", "no_mutuals": "No mutuals", "doesnt_h_a_mut_con": "doesn't have any mutual connections yet", "no_employees": "No employees", "no_employees_caption": "No people on Lobox work at this company yet", "new": "New", "pls_ent_a_v_location": "Please enter a valid location", "share_via_email": "Email", "copy_link": "Copy link", "link_copied": "<PERSON>pied", "share_job": "Share job", "no_title_entered": "No title entered", "calendar": "Calendar", "meetings": "Meetings", "reminders": "Reminders", "tasks": "Tasks", "meeting": "Meeting", "reminder": "Reminder", "task": "Task", "meeting_s": "meeting", "reminder_s": "reminder", "task_s": "task", "availability": "Availability", "availabilities": "Availabilities", "create_schedule": "Create schedule", "edit_schedule": "Edit schedule", "meeting_title_star": "Meeting title", "meeting_title": "Meeting title", "meeting_duration": "Meeting duration", "meeting_type": "Meeting type", "meeting_start_ti_zo": "Meeting start time zone", "meeting_tool_star": "Meeting tool", "meeting_tool": "Meeting tool", "meeting_reminder": "Meeting reminder", "meeting_channel": "Meeting channel", "lobox_account": "Lobox account", "lobox_account_hint": "Meeting channel will be created through the Lobox profile using Google Meet tool", "personal_account": "Personal account", "personal_account_hint": "Meeting channel will be created by your personal account", "cr_meet_via_custom_link": "Custom link", "cr_meet_via_custom_link_hint": "Meeting channel will be created by custom link", "add_account": "Add account", "paste": "Paste", "meeting_link": "Meeting link", "reminder_details": "Reminder details", "task_details": "Task details", "time": "Time", "time_star": "Time", "reminder_title_start": "Reminder title", "reminder_title": "Reminder title", "just_date": "Just date", "repeat": "Repeat", "edit_meeting": "Edit meeting", "create_meeting": "Create meeting", "create_reminder": "Create reminder", "edit_reminder": "Edit reminder", "create_task": "Create task", "edit_task": "Edit task", "task_title_star": "Task title", "task_title": "Task title", "add_attachments": "Add attachments", "attendees": "Attendees", "attendee": "<PERSON><PERSON><PERSON>", "add_attendee": "<PERSON><PERSON>", "add_attendees": "Add attendees", "attendee_permissions": "Attendee permissions", "modify_event": "Modify event", "invite_others": "Invite others", "see_guests_list": "See guest list", "address": "Address", "start_time": "Start time", "end_time": "End time", "meeting_details": "Meeting details", "all_day": "All day", "use_cur_t_z": "Use current time zone", "assignee": "Assignee", "add_assignee": "Add assignee", "meeting_add_success": "Your meeting created successfully", "task_add_success": "Your task created successfully", "reminder_add_success": "Your reminder created successfully", "creator": "Creator", "collaborators": "Collaborators", "add_collaborators": "Add collaborators", "use_description_template": "Use description template", "save_as_template": "Save as template", "edit_description": "Edit description", "change_associate": "Change associate", "associate": "Associate", "add_owners": "Add owner", "responsibilites": "Responsibilites", "requirements": "Requirements", "update": "Update", "delete_event_helper": "Are you sure you want to delete this event?", "all_jobs": "All jobs", "edit_collaborators": "Edit collaborators", "add_members_who_you_want_to_add_as_collaborator": "Add members who you want to add as collaborator", "remove_collaborator": "<PERSON><PERSON><PERSON> collaborator", "f_t_collaborator_lst": "from the collaborator list?", "collaborator_removed": "Collaborator removed", "collaborator": "Collaborator", "success_rem_f_col_lis": "successfully removed from the collaborator list", "meeting_updated_success": "Your meeting updated successfully", "task_updated_success": "Your task updated successfully", "reminder_updated_success": "Your reminder updated successfully", "remove_meeting": "Remove meeting", "r_y_s_w_r": "Are you sure that you want to remove?", "meeting_deleted_succ": "Your meeting deleted successfully", "reminder_deleted_succ": "Your reminder deleted successfully", "task_deleted_succ": "Your task deleted successfully", "suc_removed_calendar": "successfully removed from your calendar", "delete_meeting": "Delete meeting", "delete_reminder": "Delete reminder", "delete_task": "Delete task", "join_meeting": "Join meeting", "share_meeting": "Share meeting", "share_reminder": "Share reminder", "share_task": "Share task", "send_feedback": "Send feedback", "start_date_time": "Start date & time", "end_date_time": "End date & time", "past_meetings": "Past meetings", "no_meetings_sch": "No meetings scheduled", "you_dont_any_meetings_sho_y": "You do not have any upcoming or past meetings to show yet", "report_this_job": "Report this job", "no_upcoming_meetings_sch": "No upcoming meetings scheduled", "you_dont_any_upcoming_meetings_sho_y": "You do not have any upcoming meetings scheduled at the moment", "report_job": "Report job", "you_have_not_applied_to_this_job": "You have not applied to this job", "you_have_not_saved_this_job": "You have not saved this job", "this_job_too": "this job too", "scheduled_reminders": "Scheduled reminders", "y_dnt_a_reminders_at_m": "You do not have any reminders scheduled at the moment", "no_scheduled_reminder": "No scheduled reminders", "completed_reminders": "Completed reminders", "past_reminders": "Past reminders", "up_reminders": "Upcoming reminders", "open_tasks": "Open tasks", "completed_tasks": "Completed tasks", "past_tasks": "Past tasks", "no_open_tasks": "No open task", "y_dnt_h_a_open_tsks": "You do not have any open tasks assigned to you at the moment", "no_tasks": "No task", "y_dnt_h_any_task_assi": "You do not have any tasks assigned to you at the moment", "try_searching_for": "Try searching for", "answer_all_the_questions": "Answer all the questions", "no_reminders_set": "No reminders set", "y_dnt_h_a_reminder": "You do not have any reminders set at the moment", "ALL": "All jobs", "TOP_SUGGESTION": "Top suggestions", "POPULAR_JOBS": "Popular jobs", "APPLIED": "Applied", "SAVED": "Saved jobs", "OPEN": "Open", "CLOSED": "Closed", "ARCHIVE": "Archived", "ANY_TIME": "Any time", "PAST_MONTH": "Past month", "PAST_3_MONTHS": "Past 3 months", "PAST_WEEK": "Past week", "PAST_24_HOURS": "Past 24 hours", "REMOTE": "Remote", "HYBRID": "Hybrid", "ON_SITE": "On-site", "INTERNSHIP": "Internship", "ENTRY_LEVEL": "Entry level", "MIDDLE_LEVEL": "Middle level", "SENIOR_LEVEL": "Senior level", "PRINCIPAL_LEVEL": "Principal level", "ASSOCIATE": "Associate", "DIRECTOR": "Director", "EXECUTIVE": "Executive", "MOST_RECENT": "Most recent", "MOST_RELEVANT": "Most relevant", "HEALTH_CARE_PLAN": "Health care plan (Medical, Dental & Vision)", "MEDICAL_INSURANCE": "Medical insurance", "DENTAL_INSURANCE": "Dental insurance", "VISION_INSURANCE": "Vision insurance", "DISABILITY_INSURANCE": "Disability insurance", "LIFE_INSURANCE": "Life insurance", "_401_K_PLAN": "401 (k)", "PENSION_PLAN": "Pension plan", "STOCK_OPTIONS": "Stock options", "PRESCRIPTION_AND_PHARMACY_BENEFITS": "Prescription and pharmacy benefits", "MENTAL_HEALTH_COVERAGE": "Mental health coverage", "SHORT_TERM_DISABILITY_INSURANCE": "Short-term disability insurance", "LONG_TERM_DISABILITY_INSURANCE": "Long-term disability insurance", "PAID_TIME_OFF": "Paid time off (PTO)", "FAMILY_LEAVE_MATERNITY_PATERNITY": "Family leave (Maternity, Paternity)", "FAMILY_AND_MEDICAL_LEAVE": "Family and medical leave (FMLA)", "RETIREMENT_PLAN": "Retirement plan (401k, IRA)", "PAID_SICK_LEAVE": "Paid sick leave", "PAID_VACATION_TIME": "Paid vacation time", "EXTENDED_LEAVE": "Extended leave", "PAID_TRAINING_AND_DEVELOPMENT": "Paid Training and development", "COMMUTER_BENEFITS": "Commuter benefits", "STUDENT_LOAN_ASSISTANCE": "Student loan assistance", "CONTINUING_EDUCATION_ALLOWANCES": "Continuing Education allowances", "TUITION_ASSISTANCE": "Tuition assistance", "TRAVEL_AND_SPENDING_EXPENSES": "Travel and spending expenses", "LIVING_STIPENDS": "Living stipends", "WELLNESS_PROGRAMS": "Wellness programs", "HIRED": "<PERSON><PERSON>", "internal": "Internal", "external": "External", "FULL_TIME": "Full-time", "PART_TIME": "Part-time", "FREELANCE": "Freelance", "SELF_EMPLOYED": "Self employed", "max_must_be_more_than_min": "Maximum must be more than minimum", "min_must_be_less_than_max": "Minimum must be less than maximum", "this_field_can_not_contain_a_number": "This field cannot contain a number", "added_t_y_p_a_feed": "added to your profile and shared on feed", "added_t_y_p": "added to your profile", "like": "Like", "boost": "Boost", "dislike": "Dislike", "not_relevant": "Not relevant", "save_post": "Save post", "unsave_post": "Unsave post", "add_this_t_y_s_po": "Add this to your saved posts", "remove_t_p_f_collec": "Remove this post from collection", "r_y_s_y_w_t_d_p_f_co": "Are you sure you want to delete post from collection?", "hide_post": "Hide post", "hide_t_p_f_y_a": "Hide this post from your account", "unhide_t_p_t_see_i_f": "Unhide this post to see in feed", "t_of_p_notif": "Turn off post notifications", "t_on_p_notif": "Turn on post notifications", "dnt_rec_notif_f_t_p": "Disable notifications for this post", "receive_a_notif_f_t_p": "Receive notifications from this post", "edit_post": "Edit post", "edit_t_c_y_p": "Edit your post content", "delete_post": "Delete post", "permanently_d_f_y_a": "Permanently delete system", "stop_following": "Stop following", "report_post": "Report post", "im_concerned_a_t_p": "I’m concerned about this post", "tell_us_more": "Tell us more", "tnx_f_let_u_k": "Thanks for letting us know", "inform_u_a_red_f_sit_reach_o": "Inform us about any red flag situation. Reach out to local authorities for any emergency case", "w_h_re_y_r_revi_soon_let_y_k": "We have received your report and will review it as soon as possible and let you know. If the problem you are experiencing is related to the owner of this content, you can choose one of the options below", "important_warning": "Important warning!", "i_c_s_some_lif_dng_cont_loc": "In case of self-harm or someone’s life is in danger, contact the local authorities immediately", "select_lng": "Select Language", "w_r_go_p_meetings": "We are going to remove past meetings after 1 week", "w_r_go_com_reminders": "We are going to remove completed reminders after 1 week", "w_r_go_com_tasks": "We are going to remove completed tasks after 1 week", "two_fac_auth": "Two-factor authentication", "confirmation_code": "Confirmation Code", "forgot_code": "Forgot code", "enter_the_confirm_cod": "Enter the confirmation code", "follow_th_ins_o_auth_ap_t_linn_lb_acc": "Follow the instructions on the authentication app to link your Lobox account. Once the authentication app generates a confirmation code, enter it here", "CONTRACT": "Contract", "CONTRACT_TO_HIRE": "Contract to hire", "day": "Day", "week": "Week", "month": "Month", "year": "Year", "calendars": "Calendar", "meeting_tools": "Meeting tools", "submit": "Submit", "up_events": "Upcoming events", "past_events": "Past events", "no_events_sch": "No event scheduled", "you_dont_any_events_sho_y": "You do not have any upcoming or past events to show yet", "no_upcoming_events_sch": "No upcoming events scheduled", "you_dont_any_upcoming_events_sho_y": "You do not have any upcoming events to show yet", "w_r_go_p_events": "We are going to remove past events after 1 week", "birthday_details": "Birthday details", "share_birthday": "Share birthday", "holiday_details": "Holiday details", "public_holiday_details": "Public holiday details", "share_holiday": "Share holiday", "share_public_holiday": "Share public holiday", "holiday": "Holiday", "p_holiday": "Public Holiday", "remove_location": "Remove location", "y_w_n_able_acc_rec_msg": "You will no longer be able to access each other’s content and receive messages", "reject": "Reject", "e_meetings": "Meetings", "e_birthdays": "Birthdays", "e_reminders": "Reminders", "e_tasks": "Tasks", "e_public_holidays": "Public holidays", "we_ask_6dig_verf_2fa_meth_log_f_unr_dev": "We'll ask for a 6-digit verification code via 2FA security method if we notice a login from an unrecognized device or browser", "link_t_app_t_lobox": "Link the app to your Lobox account", "use_y_auth_app_sc_qr_if_d_app_d": "Use your authentication app to scan this QR code. If you don’t have an authentication app on your device, you’ll need to install one like", "if_y_c_s_qr_cam_ent_code": "If you can’t scan the QR code with your camera, enter the following code into your authentication app", "f_t_inst_auth_lin_lbx_one_gen": "Follow the instructions on the authentication app to link your Lobox account. Once the authentication app generates a confirmation code, enter it here", "y_all_set": "You’re all set", "w_ask_f_c_not_log_unrec": "We'll ask for a code if we notice an attempted login from an unrecognized device or browser", "s_t_sing_code": "Save this single-use backup code in a safe place:", "this_bk_co_y_lg_have_phon": "This backup code lets you log in to Lobox if you don’t have access to your phone for to get QR code", "holiday_title": "Holiday title", "birthday_title": "Birthday title", "turn_off_t_f_aut": "Turn-off two factor authentication?", "this_fea_prot_y_acc_w_ext": "This feature protects your account with an extra layer of security", "turn_dash_off": "Turn-off", "holidays_in": "Holidays in", "send_a_birthday_msg": "Send a birthday message!", "use_bkup_code": "Use backup code", "yr_msg_sent_success": "Your message is sent successfully", "WrongCredentialException": "Please enter a valid code", "on_cap": "On", "backup_codes": "Backup codes", "backup_code": "Backup code", "get_single_bk_cod_s_y_c_acc_two_f": "Get single-use backup codes so you can log in to Lobox if you don’t have access to your two-factor authentication options", "if_y_ev_l_ac_d_y_c_ver": "If you ever lose access to your device, you can use this code to verify your identity", "write_if_d_t_sc_k_safe_onl": "Write it down, or take a screenshot, and keep it some place safe. This code can only be used in once", "enter_the_bck_code": "Enter the backup code", "unable_t_acc_y_g_app_u_y_bck": "Unable to access your generator app? Use your backup code to securely authenticate your account", "get_bckp_code": "Get backup code", "TEMPORARY": "Temporary", "APPRENTICESHIP": "Apprenticeship", "SEASONAL": "Seasonal", "no_title": "(No title)", "no_title_no_paranthesis": "No title", "celeberate": "Celebrate", "upcoming": "Upcoming", "past": "Past", "must_b_16_y": "You must be over 16 years old", "2fa_auth": "2FA authentication", "search_in": "Search in", "switch_to_lobox_app": "Switch to Lobox app", "switch_lobox_app_now_for_a_faster_and_better_experience": "Switch Lobox app now for a faster and better experience", "not_now": "Not now", "download_app": "Download app", "see_licence_certification": "See licence & certification", "see_publication": "See publication", "see_honor_awards": "See honor & awards", "patent_number": "Patent number", "see_patent": "See patent", "no_location": "No location", "member_since": "Member since", "insights": "Insights", "collapse": "Collapse", "y_d_h_a_j_p_f_w_app": "You do not have a job posting for which you have applied", "switch": "Switch", "following_you": "Following you", "provider_star": "Provider", "institution_star": "Institution", "presented_by_star": "Presented by", "major_star": "Major", "publisher_star": "Publisher", "no_top_suggestions": "No top suggestion", "no_popular_jobs": "No popular jobs", "all_pages": "All pages", "all_people": "All people", "discover_pages_to_get_top_suggestions": "Discover top suggested pages", "discover_jobs_to_get_top_suggestions": "Discover top suggested jobs", "discover_people_to_get_top_suggestions": "Discover top suggested people", "discover_jobs_to_get_popular_jobs": "Discover popular jobs", "no_managed_pages": "No managed page", "you_are_not_managing_any_page": "You are not managing any page", "no_followed_pages": "No followed page", "you_are_not_following_any_page": "You are not following any page", "no_popular_hashtags": "No popular hashtag", "there_are_no_popular_hashtags": "There is no popular hashtag", "no_hashtag_followed": "No hashtag followed", "you_are_not_following_any_hashtag": "You are not following any hashtag", "to_do_that_please_follow_these_instructions": "To do that, please follow these instructions", "install_lobox_app": "Install Lobox App", "tap-the-action-icon": "Tap the action icon", "tap_the_add_to_home_screen": "Tap the 'Add to Home Screen'", "type_the_name_of_the_app": "Type the name of the App", "tap_the_3_dots_menu": "Tap the 3 dots menu", "tap_the_install_app": "Tap the 'Install app'", "tap_the_install_on_openning_modal": "Tap the 'Install' on the openning modal", "how_to_install_lobox_app": "How to install Lobox app?", "bachelors_degree": "Bachelor Degree", "masters_degree": "Master Degree", "DOCTORAL": "Doctoral Degree", "associates_degree": "Associate Degree", "POSTDOCTORAL": "Postdoctoral Fellowship", "HIGH_SCHOOL": "High School Diploma", "MIDDLE_SCHOOL": "Middle School Diploma", "ELEMENTARY": "Elementary", "PROFESSIONAL": "Profesional Degree", "DIPLOMA": "Diploma", "HONORARY": "Honorary Degree", "NON": "Non-Degree", "under_review": "Under Review", "selected_for_interview": "Selected for Interview", "interviewed": "Interviewed", "reference_check": "Reference Check", "offer": "Offer", "rejected": "Rejected", "hourly": "Hourly", "daily": "Daily", "bi_weekly": "Bi-Weekly", "yearly": "Yearly", "a1": "<PERSON><PERSON><PERSON>", "a2": "Elementary", "b1": "Intermediate", "b2": "Upper Intermediate", "c1": "Advanced", "c2": "Proficiency", "native": "Native", "part_time": "Part-time", "contract": "Contract", "contract_to_hire": "Contract to hire", "self_employed": "Self employed", "freelance": "Freelance", "temporary": "Temporary", "internship": "Internship", "apprenticeship": "Apprenticeship", "seasonal": "Seasonal", "entry_level": "Entry level", "middle_level": "Middle level", "senior_level": "Senior level", "principal_level": "Principal level", "director": "Director", "executive": "Executive", "most_relevant": "Most relevant", "beginner": "<PERSON><PERSON><PERSON>", "intermediate": "Intermediate", "advanced": "Advanced", "any_time": "Any time", "past_month": "Past month", "past_week": "Past week", "past_24_hours": "Past 24 hours", "no_applied_jobs": "No applied jobs", "no_saved_jobs": "No saved jobs", "no_items_to_display": "No items to display", "no_job_items": "No job items", "shared_your": "shared your", "tag_you_on_a": "tagged you on a", "mention_you_on_a": "mentioned you on a", "tip_cap": "Tip", "add_profile_photo": "Add a profile photo", "add_profile_photo_info": "Adding a profile photo would help you present yourself better", "add_cover_img_info": "Adding a cover image would help you present yourself better", "update_profile": "Update profile", "update_profile_info": "Adding more information to your profile would increase visibility", "complete_profile": "Complete profile", "complete_profile_info": "Completed profiles getting more job offers than average. Let us help you to complete your profile", "job_switch_open": "Turn Open for job opportunities switch on to be able to discover by recruiters and other professionals", "your_follow_req_sent": "Your follow request sent", "you_started_following": "You started following", "you_declined_following": "You declined following", "suggestion_cap": "Suggestion", "you_may_know": "You may know {user_title}", "do_you_want_to_follow": "Do you want to follow?", "also_attended": "also attended", "also_joined": "also joined", "you_accepted_role": "You accepted role request", "you_declined_role": "You declined role request", "you_accepted_follow": "You accepted follow request", "you_declined_follow": "You declined follow request", "password_changed": "Password has changed", "your_password_changed": "Your password changed", "this_not_you": "This is not you?", "recover_your_password": "Recover your password", "recover_password": "Recover password", "saved_not_applied": "You have saved some jobs however not applied yet", "you_have": "You have", "new_candidate_recommendations": "New candidate recommendations", "candidates": "Candidates", "tap_to_action_icon": "Tap to action icon", "tap_to_add_to_home_screen": "Tap to \"Add to Home Screen\"", "tap_to_3_dots_menu": "Tap to 3 dots menu", "tap_to_nav_menu": "Tap to nav menu", "tap_to_share": "Tap to share", "your_daily": "Your daily", "top_suggestions_jobs": "Top suggestions jobs", "updated": "updated", "view_top_suggestions": "View Top suggestions", "applied_for": "applied for", "profiles": "Profiles", "footer_links": "Footer links", "removed": "removed", "declined": "declined", "invite_again": "Invite again", "confirmed": "confirmed", "invited_you": "invited you", "is_going_to_start_in": "is going to start in", "minutes": "minutes", "delete_chat": "Delete chat", "conversation_deleted": "Conversation deleted", "delete_group_succ": "Group deleted successfully", "search_mutuals": "Search", "search_employees": "Search employees", "share_profile": "Share profile", "block_name": "Block <b>{name}</b>", "use_lobox_as_name": "Use Lobox as {name}", "share_post": "Share Post", "remove_reminder": "Remove reminder", "remove_task": "Remove task", "remove_birthday": "Remove birthday", "remove_holiday": "Remove holiday", "x_ago": "%s ago", "a_few_seconds": "A few seconds", "a_min": "A min", "x_minutes": "%d minutes", "an_hour": "An hour", "x_hours": "%d hours", "a_day": "A day", "x_days": "%d days", "a_month": "A month", "x_months": "%d months", "a_year": "A year", "x_years": "%d years", "in_x": "in %s", "name_is_with_name": "<b>{name}</b> is with <b>{name}</b>", "name_is_with_name_and_name": "<b>{name}</b> is with <b>{name}</b> and <b>{name}</b>", "name_is_with_name_in_location": "<b>{name}</b> is with <b>{name}</b> is in <b>{location}</b>", "name_is_with_name_and_others": "<b>{name}</b> is with <b>{name}</b> and <b>{number}</b>", "name_is_at_location": "<b>{name}</b> is in <b>{location}</b>", "name_shared_entity": "<b>{name}</b> shared a <b>{post}</b>", "preferences": "Preferences", "sub_preferences": "Feed preferences, Career preferences, other preferences", "home_preferences": "Home preferences", "people_preferences": "People preferences", "pages_preferences": "Pages preferences", "schedule_preferences": "Schedule preferences", "message_preferences": "Message preferences", "jobs_preferences": "Jobs preferences", "preferences_subtitle_description": "New reactions, comments, replies, shares, tags and mentions", "schedule_preferences_subtitle_description": "General, calendar, meetings, reminders, tasks, events", "calendar_preferences": "Calendar preferences", "general": "General", "default_time_formats": "Default time formats", "view_integrations": "View, integrations", "general_audio_video_integrations": "General, audio, video, integrations", "birthdays_workshops": "Birthdays, workshops", "date_time": "Date & time", "duplicate": "Duplicate", "duplicate_skill_confirm": "This is going to override the existing one with this skill, are you sure?", "in_your_saved_jobs": "in your saved jobs", "no_availability": "No availability", "you_do_not_have_any_availability": "You do not have any availability", "new_availability": "New availability", "remove_availability": "Remove availability", "are_you_sure_you_want_to_remove_the_availability": "Are you sure you want to remove the availability?", "availability_removed": "Availability removed", "availability_removed_successfully": "Availability removed successfully", "availability_created": "Availability created", "availability_created_successfully": "Availability created successfully", "availability_changed": "Availability changed", "availability_changed_successfully": "Availability changed successfully", "availability_name": "Availability name", "share_availability": "Share availability", "available_days": "Available days", "available_times": "Available times", "availability_suc_shared": "Available successfully shared", "enter_details": "Enter details", "time_zone": "Time zone", "additional_information": "Additional information", "schedule_meeting": "Schedule meeting", "meeting_suc_scheduled": "Meeting Successfully scheduled", "meeting_scheduled": "Meeting scheduled", "availability_details": "Availability details", "Wendy_worked_with_Juanita_directly1": "{username} managed {visitor} directly", "Wendy_worked_with_Juanita_directly2": "{username} reported directly to {visitor}", "Wendy_worked_with_Juanita_directly3": "{username} was senior to {visitor} but didn’t manage {visitor} directly", "Wendy_worked_with_Juanita_directly4": "{visitor} were senior to {username} but didn’t manage directly", "Wendy_worked_with_Juanita_directly5": "{visitor} worked with {username} in the same group", "Wendy_worked_with_Juanita_directly6": "{visitor} managed {username} directly", "Wendy_worked_with_Juanita_directly8": "{username} worked with {visitor} but at different companies", "Wendy_worked_with_Juanita_directly9": "{visitor} were a client of {username}’s", "Wendy_worked_with_Juanita_directlya": "{username} was a client of {visitor}’s", "Wendy_worked_with_Juanita_directlyb": "{username} taught {visitor}", "Wendy_worked_with_Juanita_directlyc": "{username} mentored {visitor}", "Wendy_worked_with_Juanita_directlye": "{visitor} were students together", "checking_verif_code": "Checking verification Code", "share_page": "Share page", "add_tags": "Add tags", "message_sent": "Message sent", "task_sent": "Task sent", "meeting_sent_success": "{meeting} meeting is successfully sent", "task_sent_success": "{task} task is successfully sent", "view_messages": "View messages", "one_last_step": "One last step", "now_to_attend_the_meeting": "now to attend the meeting!", "meeting_not_found": "Meeting not found", "task_not_found": "Task not found", "reminder_not_found": "Reminder not found", "event_not_found": "Event not found", "meeting_not_exist": "This meeting no longer exists", "task_not_exist": "This task no longer exists", "reminder_not_exist": "This reminder no longer exists", "event_not_exist": "This event no longer exists", "view_meeting": "View meeting", "view_task": "View task", "PAST_YEAR": "Past year", "PAST_6_MONTHS": "Past 6 months", "post_type": "Post type", "PHOTO": "Photo", "TEXT": "Text only", "VIDEO": "Video", "popular_people": "Popular people", "COMPANY_SIZE_A": "0-1 person", "COMPANY_SIZE_B": "2-10 people", "COMPANY_SIZE_C": "11-50 people", "COMPANY_SIZE_D": "51-200 people", "COMPANY_SIZE_E": "201-500 people", "COMPANY_SIZE_F": "501-1000 people", "COMPANY_SIZE_G": "1001-5000 people", "COMPANY_SIZE_H": "5001+ people", "COMPANY_TYPE_ARTIST": "Artist - Celebrity", "COMPANY_TYPE_COMMUNITY": "Community Page", "COMPANY_TYPE_COMPANY": "Company", "COMPANY_TYPE_EDUCATION_INSTITUTION": "Educational Institution", "COMPANY_TYPE_INSTITUTION": "Institution", "COMPANY_TYPE_NON_PROFIT_ORGANIZATION": "Non-Profit Organization", "COMPANY_TYPE_PUBLIC_FIGURE": "Public Figure", "COMPANY_TYPE_PUBLIC_SERVICE": "Public Service", "hired_jobs": "Hired jobs", "SAT": "SAT", "SUN": "SUN", "MON": "MON", "TUE": "TUE", "WED": "WED", "THU": "THU", "FRI": "FRI", "hour_pm": "{hour} PM", "hour_am": "{hour} AM", "January": "January", "February": "February", "March": "March", "April": "April", "May": "May", "June": "June", "July": "July", "August": "August", "September": "September", "October": "October", "November": "November", "December": "December", "last_seen_long_ago": "Last seen a long time ago", "last_seen_recently": "Last seen recently", "last_seen_week": "Last seen within a week", "last_seen_month": "Last seen within a month", "share_yr_tho_ph_and_vid": "Share your ideas and media with your followers", "share_yr_ex_and_pass": "Share your expertise and passions", "share_breaking_news_anc_capt": "Share breaking news and captivating stories", "showcase_yr_best_mom_lif": "Showcase your best moments in life", "create_pg_f_y_cmp_ins": "Create a page for your organization", "recruit_top_tal_f_yr_pr": "Hire top talents for your projects", "content_an_engage_with_lik_indiv": "Connect with like-minded individuals", "efficiently_manag_yr_tim_even": "Efficiently manage your time using our amazing tool", "job_cap": "Job", "schedule_cap": "Schedule", "user_set_pers_info_visi": "User settings, personal information, visibility", "chnge_pass_lgin_iss_log": "Change password, login issues, logged devices", "privacy_agre_dta_saf": "Privacy agreement, data safety, 3rd party policy", "work_status_profile_set_re": "Work status, profile setting, resume options", "page_setting_page": "Page settings, page roles, etc", "network_relation_report": "Network relations, reporting", "inappropriate_content": "Inappropriate content", "intellectual_property_viola": "Intellectual property violation", "spam_or_misleading_info": "Spam or misleading information", "personal_info_expo": "Personal information exposure", "nudity_or_sex_content": "Nudity or sexual content", "violence_or_graphic_cont": "Violence or graphic content", "hate_speech_or_discrim": "Hate speech or discriminatory language", "harassment_or_bullying": "Harassment or bullying", "offensive_or_abusiv": "Offensive or abusive behavior", "plagiarism_or_copright": "Plagiarism or copyright infringement", "unauth_us_of_tradma": "Unauthorized use of trademarks or logos", "stolen_or_unauth": "Stolen or unauthorized content", "intellectual_property_voil": "Intellectual property violation", "exc_self_or_spam": "Excessive self-promotion or spamming", "false_or_misleading_info": "False or misleading information", "scam_or_fraud_cont": "Scam or fraudulent content", "sharing_personal_sent_info": "Sharing personal or sensitive information without consent", "invasion_of_privacy": "Invasion of privacy", "other_cap": "Other", "fake_or_imper_acc": "Fake or impersonating account", "someone_petending_to_be_som": "Someone pretending to be someone else", "fake_or_mis_profile_info": "Fake or misleading profile information", "inappropriate_behavior": "Inappropriate behavior", "spamming_or_ex_msg": "Spamming or excessive messaging", "fake_or_mis_page": "Fake or misleading page", "page_imper_per_org": "Page impersonating a person or organization", "scam_or_fraud_job": "Scam or fraudulent job", "suspic_or_fraud_job_post": "Suspicious or fraudulent job postings", "req_f_person_fina_info": "Requests for personal or financial information", "inapprop_job_content": "Inappropriate job content", "mis_or_false_job_desc": "Misleading or false job descriptions", "dis_job_post": "Discriminatory job postings", "inapp_job_req": "Inappropriate job requirements", "unauth_use_of_tradmark_logo_job_post": "Unauthorized use of trademarks or logos in job postings", "edit_availability": "Edit availability", "integrations": "Integrations", "microsoft": "Microsoft", "outlook": "Outlook", "google_calendar": "Google Calendar", "office_365_calendar": "Office 365 Calendar", "outlook_calendar": "Outlook Calendar", "connected": "Connected", "disconnected": "Disconnected", "image": "Image", "repeated_unwanted_msg": "Repeated unwanted messages", "threatening_abus_lng": "Threatening or abusive language", "no_phone_entered": "No phone entered", "no_cover_letter_entered": "No cover letter entered", "no_resume_entered": "No resume entered", "no_answer_entered": "No answer entered", "pls_crt_pg_first_t_p_y_j": "Please create a page first to post your job and start hiring", "HIGHLIGHT": "Highlight", "CHECK_IN": "Check-in", "text_cap": "Text", "CouldNotFetchItemsException": "Please check all the access boxes", "time_is_passed": "Time is passed", "user_cap": "User", "view_details": "View details", "notes": "Notes", "meeting_type_star": "Meeting type", "attendee_star": "<PERSON><PERSON><PERSON>", "add_attendees_star": "Add attendees", "end_after_start": "Ending should be after start", "no_upcoming_scheduled_items": "No upcoming scheduled items", "you_not_have_upcoming_schedule_items": "You do not have upcoming scheduled items", "no_past_scheduled_items": "No past scheduled items", "you_not_have_past_schedule_items": "You do not have past scheduled items", "gallery": "Gallery", "images_count": "{count} images", "no_du_capt": "No duration captured", "send_holiday_greetings": "Send holiday greetings!", "celebrated_successfully": "<PERSON><PERSON><PERSON><PERSON> successfully", "celebration_message_sent_successfully": "Celeberation message sent successfully", "event_details": "Event details", "send_birthday_message": "Send a birthday message!", "no_media_available": "No media available", "add_creator": "Add creator", "search_creator": "Search creator", "no_post_filter_f_wi_entry": "No post found with the current filters", "no_page_filter_f_wi_entry": "No page found with the current filters", "no_people_filter_f_wi_entry": "No people found with the current filters", "no_people": "No people!", "no_pages": "No pages!", "new_volunteering": "New volunteering", "new_volunteering_desc": "Unleash volunteering tales", "skill": "Skill", "NEW_SIMPLE": "New highlight", "NEW_JOB": "New job", "NEW_GET_PROMOTED": "Get promoted", "NEW_ENTREPRENEURSHIP": "New entrepreneurship", "NEW_SCHOOL": "New school", "NEW_COURSE": "New course", "NEW_GRADUATION": "New graduation", "NEW_STUDY_ABROAD": "New study abroad", "NEW_TRAVEL": "New travel", "NEW_INSTRUMENT": "New instrument", "NEW_HOBBY": "New hobby", "NEW_SPORT": "New sport", "NEW_LICENSE": "New license", "NEW_GET_FUNDED": "New found", "NEW_PATENT": "New patent", "NEW_PUBLICATION": "New publication", "NEW_LANGUAGE": "New language", "NEW_AWARD": "New award", "NEW_CERTIFICATION": "New certification", "NEW_SKILL": "New skill", "NEW_ADVENTURE": "New adventure", "NEW_CONFERENCE_PARTICIPATION": "New conference", "NEW_ACQUIRED": "New acquired", "NEW_NEW_LOCATION": "New location", "NEW_CHANGED_NAME": "New name", "NEW_CHANGED_LOGO": "New logo", "NEW_MARKET_VALUE": "New market value", "NEW_STOCK_VALUE_CHANGE": "New stock value", "NEW_GET_INVESTED": "Get invested", "NEW_IPO": "New IPO", "NEW_ANNIVERSARY": "New anniversary", "NEW_NEW_TEAM_MEMBER": "New team member", "BACHELOR": "Bachelor Degree", "MASTER": "Master Degree", "MaximumRolePerPageExceedException": "The maximum limit for attaching role to a user is 2", "this_meeting_set_past": "This meeting occurs in the past", "this_reminder_set_past": "This reminder occurs in the past", "this_task_set_past": "This task occurs the past", "exchange_calendar": "Exchange Calendar", "no_attendees_added": "No attendees added", "no_assignees_added": "No assignees added", "no_collaborators_added": "No collaborators added", "no_scheduled_events": "There are no scheduled events on this day", "remove_account": "Remove account", "account_removed": "Account removed", "are_you_sure_want_remove": "Are you sure you want to remove?", "account_lower": "account", "from_your": "from your", "integration": "integration", "removed_from_your": "removed from your", "you_can_connect_email_to_bring_your_events": "You can connect your email accounts with the providers below to automatically bring all your scheduled events here", "invalid_time_format": "Invalid time format", "page_lng": "Page language", "page_lng_star": "Page language", "see_less": "See less", "icloud": "iCloud", "exchange": "Exchange", "icloud_calendar": "iCloud Calendar", "set_availability": "Set availability", "availability_preferences": "Availability preferences", "attachments": "Attachments", "user_details": "User details", "meeting_model_star": "Meeting model", "meeting_model": "Meeting model", "lobox": "Lobox", "google_meet": "Google Meet", "zoom": "Zoom", "microsoft_teams": "Microsoft Teams", "teams": "Teams", "skype": "Skype", "webex": "Webex", "assignee_permission": "Assignee permission", "edit_attendees": "Edit attendees", "modify_meeting": "Modify meeting", "modify_task": "Modify task", "address_details": "Address details", "room": "Room", "example_address": "ie: 2nd floor, room: Yosemite", "on_site_address": "On site address", "view_on_map": "View on map", "schools": "Schools", "scope": "<PERSON><PERSON>", "select_one_of_sug_locns": "Select one of the suggested locations", "no_content_found": "No content found", "t_p_d_n_h_a_p_or_h_t_d": "This profile does not have any posts or highlights to display", "t_p_d_n_h_a_p_t_d": "This profile does not have any posts to display", "t_p_d_n_h_a_h_t_d": "This profile does not have any highlights to display", "t_c_l_h_b_c_t_y_c": "The comment link has been copied to your clipboard", "editing_comment": "Editing comment", "your_followers": "Your followers", "your_following": "Your following", "smileys_people": "Smileys & People", "animals_nature": "Animals & Nature", "food_drink": "Food & drink", "travel_places": "Travel & Places", "activities": "Activities", "objects": "Objects", "symbols": "Symbols", "flags": "Flags", "jobs_f_in": "jobs found in", "via_y_j_alert": "via your job alerts", "view_count_jobs": "View +{name} jobs", "closed_the": "closed the", "if_y_t_oc_b_m_y_c": "If you think this occurred by mistake, you can unblock the", "PRO_EXPERIENCE": "Pro experience", "VOL_EXPERIENCE": "Vol experience", "BIO": "Bio", "EDUCATION": "Education", "COURSE": "Course", "EDIT_PROFILE": "Edit profile", "SCHOOL": "School", "LICENCE": "Licence", "ACCOMPLISHMENT": "Accomplishment", "PUBLICATION": "Publication", "HONOR": "Honor", "PATENT": "Patent", "ADD_SECTION": "Add section", "ASK_RECOMMENDATION": "Ask recommendation", "SKILL_UPSERT": "Skill upsert", "RESUME": "Resume", "LANGUAGE_UPSERT": "Language upsert", "DATE_OF_BIRTH": "Date of birth", "PHONE_NUMBER": "Phone number", "LOCATION": "Location", "WEB_SITE": "Web site", "FOLLOW": "Follow", "FOLLOWING": "Following", "REQUEST": "Request", "FOLLOWERS": "Followers", "ASK_REVISION": "Ask revision", "RECEIVED_RECOMMENDATION": "Received recommendation", "GIVEN_RECOMMENDATION": "Given recommendation", "REQUEST_RECOMMENDATION": "Request recommendation", "SHARE_RESUME_VIA_MESSAGE": "Share resume via message", "CREATE_PAGE_FORM": "Create page form", "no_collection_saved": "No collection saved", "name_req_y_t_b_name_of_name_page": "<b>{name}</b> requested you to be <b>{name}</b> of <b>{name}</b> page", "report_meeting": "Report meeting", "report_user": "Report user", "report_page": "Report page", "report_message": "Report message", "comment_deleted_sucss": "Comment deleted successfully", "relationship": "Relationship", "your_message": "Your message", "search_profile": "Search profile", "write_cap": "Write", "biography": "Biography", "add_some_details_ab_y": "Add some details about who you are", "help_hiring_mng_c_f_y": "Help hiring managers and co-workers find you", "volunteers": "Volunteers", "unlesh_vol_tal": "Unleash volunteering tales", "add_y_sch_so_th_clas_c_f_y": "Show your education level", "y_c_as_or_wr_recom": "Write or request a recommendation", "your_name_sent_to_name": "Your {name} sent to {name}", "recom_req": "recommendation request", "salary_range": "Salary range", "incoming_requests": "Incoming requests", "PROFILE_CITY": "Profile city", "PROFILE_COUNTRY": "Profile country", "NEARBY_COUNTRY": "Nearby country", "related_with": "Related with", "posted_by": "Posted by", "FILTER_ME": "Posted by me", "FILTER_YOU": "Posted by you", "FILTER_FOLLOWING": "Following only", "FILTER_PHOTO": "Image", "FILTER_VIDEO": "Video", "FILTER_GALLERY": "Gallery", "FILTER_HIGHLIGHT": "Highlight", "FILTER_CHECK_IN": "Check-in", "FILTER_JOB": "Job", "FILTER_PAGE": "Page", "FILTER_PEOPLE": "People", "FILTER_TEXT": "Text", "FILTER_ANY_TIME": "Any time", "FILTER_PAST_YEAR": "Past year", "FILTER_PAST_6_MONTHS": "Past 6 months", "FILTER_PAST_MONTH": "Past month", "FILTER_PAST_WEEK": "Past week", "FILTER_PAST_24_HOURS": "Past 24 hours", "FILTER_MOST_RELEVANT": "Most relevant", "FILTER_MOST_RECENT": "Most recent", "hi_name_would_y_write_recom": "Hi {name} would you write a recommendation for me?", "your_recom_sent_name": "Your recommendation sent to {name}", "recom_dec": "Recommendation Declined", "you_dec_name_recom": "You declined {name} recommendation", "recom_accept": "Recommendation Accepted", "y_accept_name_recom": "You accepted {name} recommendation", "RECOMMENDATION_PENDING": "Pending", "RECOMMENDATION_ACCEPTED": "Accepted", "RECOMMENDATION_DECLINED": "Declined", "RECOMMENDATION_ASKED": "Requested", "delete_recommendation": "Delete recommendation", "revise_recom": "Revise recommendation", "name_role_at_the_time": "{name} position at the time", "VOLUNTEER": "Volunteer", "y_blocked_t_profile": "You blocked this profile", "i_y_t_t_mistake_un_user": "If you think this occurred by mistake, you can unblock the user", "i_y_t_t_mistake_un_page": "If you think this occurred by mistake, you can unblock the page", "search_benefits": "Search benefits", "shared_sm": "shared", "profile": "Profile", "you_h_unb_name": "Your have unblocked <b>{name}</b>", "starred": "Starred", "applied_b_y": "Applied by you", "new_experience": "New experience", "select_experience": "Select experience", "remove_highlight": "Remove highlight", "are_y_s_w_remove_h": "Are you sure that you want to remove highlight from post?", "highlight_volunteering": "New volunteer job", "select_volunteer": "Select volunteer", "select_school": "Select school", "achievements": "Achievements", "select_course": "Select course", "cause": "Cause", "organization": "Organization", "currently_studying_here": "Currently studying here", "graduation": "Graduation", "new_certification_desc": "Share your new identities", "select_certification": "Select license & certification", "sport_title": "Sport title", "select_publication": "Select publication", "issued_date": "Issued date", "select_honor_award": "Select honor & award", "select_patent": "Select patent", "patent_title": "Title", "obtain_date": "Obtain date", "select_skill": "Select skill", "select_language": "Select language", "exmp_ang_f_sec": "Example: Angles fund secured", "exp_da_aro_th_wor": "Example: 80 days around the world", "exmp_inter_conf_con": "Example: International Confex conference", "old_name_of_pg": "Old name", "expm_mrg_annivers": "Example: Marriage anniversary", "team_member": "Team member", "ENVIRONMENT_AND_SUSTAINABILITY": "Environment and Sustainability", "EDUCATION_AND_LITERACY": "Education and Literacy", "POVERTY_ALLEVIATION": "Poverty Alleviation", "HEALTH_AND_WELLNESS": "Health and Wellness", "ANIMAL_WELFARE": "Animal Welfare", "COMMUNITY_DEVELOPMENT": "Community Development", "HUMAN_RIGHTS_AND_SOCIAL_JUSTICE": "Human Rights and Social Justice", "DISASTER_RELIEF_AND_EMERGENCY_RESPONSE": "Disaster Relief and Emergency Response", "ARTS_AND_CULTURE": "Arts and Culture", "ELDERLY_CARE_AND_SUPPORT": "Elderly Care and Support", "CHILDREN_AND_YOUTH_EMPOWERMENT": "Children and Youth Empowerment", "GENDER_EQUALITY_AND_WOMEN_RIGHTS": "Gender Equality and Women Rights", "HOMELESSNESS_AND_HOUSING_SUPPORT": "Homelessness and Housing Support", "FOOD_SECURITY_AND_HUNGER_RELIEF": "Food Security and Hunger Relief", "MENTAL_HEALTH_AND_WELLBEING": "Mental Health and Wellbeing", "DISABILITY_RIGHTS_AND_INCLUSION": "Disability Rights and Inclusion", "TECHNOLOGY_AND_DIGITAL_LITERACY": "Technology and Digital Literacy", "CONSERVATION_AND_WILDLIFE_PROTECTION": "Conservation and Wildlife Protection", "LGBTQ_RIGHTS_AND_ADVOCACY": "LGBTQ Rights and Advocacy", "INDIGENOUS_RIGHTS_AND_CULTURAL_PRESERVATION": "Indigenous Rights and Cultural Preservation", "via_email": "Via email", "via_message": "Via message", "via_post": "Via post", "city": "City", "search_mentions": "Search mentions", "profile_sm": "profile", "news": "News", "group_sm": "group", "comment_deleted": "Comment deleted", "comment_deleted_desc": "Your comment has been successfully deleted", "NEW_VOLUNTEERING": "New volunteering", "remove_image": "Remove image", "remove_video": "Remove video", "are_y_s_w_remove_img": "Are you sure that you want to remove image from post?", "are_y_s_w_remove_video": "Are you sure that you want to remove video from post?", "add_new_experience": "Add new experience", "surname": "Surname", "in_order_to_change_email_and_phone_will_redirect_settings": "In order to change email and phone you will be redirected to settings", "edit_sections": "Edit sections", "add_volunteer": "Add volunteer", "edit_volunteer": "Edit volunteer", "experiences": "Experiences", "help_hiring_teams_c_f_y": "Help hiring teams and co-workers find you", "educations": "Educations", "add_your_educational_bg": "Add your educational background", "let_the_word_know_your_languages": "Let the world know how many languages you speak", "add_your_accomplishments": "Add your accomplishments to your profile", "ask_people_to_recommend_you": "Ask people to recommend you", "delete_item_confirmation": "Are you sure you want to delete this item?", "example_m_new_mil": "Example: My new milestone", "licence_certificate": "Licence & certification", "travel": "Travel", "instrument": "Instrument", "hobby": "<PERSON>bby", "sport": "Sport", "adventure": "Adventure", "volunteering": "Volunteering", "add_recommendation": "Add recommendation", "would_you_write_recommendation_for_me": ", would you write a recommendation for me?", "duplicate_recommendation_error": "This account has already written a position for this position", "role_at_the_time": "Position at the time", "your_collections": "Your collections", "would_you_like_to_post_your_new": "Would you like to post your new {highlight}?", "your_recomendation_request_sent": "Your recommendation request sent to {guy}", "you_asked_revision_from": "You asked a revision from {guy}", "you_revised_recommendation": "You revised recommendation sent to{guy}", "edit_contacnt": "Edit contact", "your_recommendation_sent": "Your recommendation sent to {guy}", "clicking_next_will_overwrite_your_current_profile": "Clicking next will overwrite your current profile", "are_you_sure_you_want_to_delete_your_entity": "Are you sure you want to remove your {entity}?", "or_fill_profile_with_resume": "Or fill profile with resume", "uploading_linkedin_resume_results_in_100_accuracy": "Uploading LinkedIn resume results in a 100% accuracy", "drag_and_drop_your_resume_here": "Drag and drop your resume here", "or_lower": "or", "share_on_profile": "Share on profile", "new_collection": "New collection", "COMPANY_SIZE_A_value": "0-1", "COMPANY_SIZE_B_value": "2-10", "COMPANY_SIZE_C_value": "11-50", "COMPANY_SIZE_D_value": "51-200", "COMPANY_SIZE_E_value": "201-500", "COMPANY_SIZE_F_value": "501-1000", "COMPANY_SIZE_G_value": "1001-5000", "COMPANY_SIZE_H_value": "5001", "recommended_skills": "Recommended skills", "remove_follower": "Remove follower", "do_you_want_us_to_help_you_update_your_profile": "Do you want us to help you updating your profile with your uploaded resume?", "fill_profile": "Fill profile", "point_of_contact": "Point of contact", "created_jobs": "Created jobs", "too_many_attempts": "Too many attempts", "unexpected_error": "Unexpected error", "this_field_is_required": "This field is required", "upload_process_failed": "Upload process failed", "page_employees": "Page employees", "page_information": "Page information", "no_people_found": "No people found", "y_d_h_a_following_d_s_people": "You don’t have any following, Discover some people", "y_d_h_a_follower_d_s_people": "You don’t have any followers, Discover some people", "y_d_h_a_following_d_s_pages": "You don’t have any following, Discover some pages", "y_d_h_a_follower_d_s_pages": "You don’t have any followers, Discover some pages", "edit_cover": "Edit cover", "create_page_hashtag_tooltip": "Adding hashtag increases the chance of your page being seen more to users", "no_pages_found": "No pages found", "r_y_s_w_r_name_follower_list": "Are you sure you want to remove {user} from your follower's list?", "page_deleted": "Page deleted", "straighten": "<PERSON>en", "drag_to_reposition": "Drag to Reposition", "anyone": "anyone", "your_x_is_x": "Your {x} is public to {x}", "y_page_deleted_successfully": "Your page deleted successfully", "thrive_friendship_with_lobox_invites": "Thrive friendship with <PERSON><PERSON> invites", "you_can_control_who_to_connect_and_concatcs_anytime": "You control who to connect, and manage your contacts anytime", "use_invitation_link_or_qr_for_faster": "Use invitation link or qr to invite faster", "provide_your_uniqe_link_can_be_easily_shared_audience": "Provide your unique invitation link, which can be easily copied and shared with your audience", "copy_invitation_link": "Copy invitation link", "sync_with_google": "Sync with google", "google_invite": "Google invite", "email_invite": "Email invite", "maximum_multi_submissions": "Maximum multi-submissions are up to 50.", "any_comment_about_job": "Any comment about this job?", "multiJobSubmittedTitle": "Job submitted", "add_cooment": "Add comment", "multiJobSubmittedSubTitle": "Your job submitted to your vendor(s) successfully.", "search_submit_vendor": "Search company by ID, title or username", "google_contacts": "Google contacts", "you_need_to_connect_your_google_to_load_contacts": "You need to connect your google account to load your contacts", "add_google_account": "Add Google account", "google_account": "Google account", "enjoy_our_built_in_invites_or_bulk_emails_to_invute": "Enjoy our built-in invites or use bulk emails to grow your circle of invitations", "or_send_bulk_emails": "or send bulk emails", "upload_file": "Upload file", "file_types_xls_csv": "Accepted file types: .xls, xlsx, and .csv", "your_file_contains_count_emails": "Your file contains {count} emails", "delete_file": "Delete file", "are_you_sure_want_to_delete_file": "Are you sure you want to delete your uploaded file?", "you_can_proceed_with_only_one_method": "You can proceed with only one method", "bring_your_community_unique_ecosystem": "Bring your community to this unique ecosystem", "it_may_take_few_hours_to_emaill_all_invitations": "It may take a few hours to email all invitations", "send_as": "Send as", "invite_message": "Invite message", "your_contact_will_see_this_message": "Your contact will see this message", "anonymous_message_to_invite_people": "Hi, a user of Lobox would like to extend an invitation for you to join our community. You can do so by clicking on the link provided in this email. Wishing you joyful moments", "user_sent_message_to_invite_people": "Hi, I would like to extend an invitation for you to join Lobox. You can do so by clicking on the link provided in this email. Wishing you joyful moments", "you_can_send_invitation_as_yourself_or_lobox": "You can send invitation as yourself or from Lobox", "invitation_completed": "Invitation Completed", "invitation_requests_to_email_contacts_have_been_sent": "Invitation requests to your email contacts have been successfully sent", "invitation_requests_to_google_contacts_have_been_sent": "Invitation requests to your Google contacts have been successfully sent", "google_user": "Google user", "cancel_request": "Cancel request", "r_y_s_w_d_y_follow_req": "Are you sure you want cancel your follow request", "profiles_y_d_f_b": "Profiles you don’t follow back", "follow_b_p": "Follow back profiles", "other_plural": "others", "other_singular": "other", "and_number_others": "and {number}", "make_t_y_p_loc": "Make this your primary location", "control_multi_p_at_o": "Control multiple profiles at once", "manage": "Manage", "delete_account_confirmation": "Are you sure you want to delete this account?", "edit_contact": "Edit contact", "HashGenerationException": "Storing resume in database unsuccessful, try again", "MaximumResumeAttemptsExceededException.message": "You have exceeded upload limit, try later", "PreviousDataMapException.message": "Retrieving previous data was unsuccessful, try later", "ResumeFileLoadingException.message": "Failed to receive your resume file, try later", "ResumeFileTooLargeException.message": "Length limit exceeded, try a shorter version", "ResumeParseException.message": "Unrecognizable format, try another resume", "MaximumResumeAttemptsExceededException.title": "Exceeding Upload Limit", "ResumeFileLoadingException.title": "Upload failed", "ResumeFileTooLargeException.title": "Exceeding Length Limit", "manage_follow_b_l": "Manage Follow back list", "follower_plural": "followers", "follower_singular": "follower", "multi_follow": "Multi follow", "r_y_s_y_w_t_f_number_profile": "Are you sure you want to follow {number} {profile}?", "r_y_s_y_w_t_r_number_follower": "Are you sure you want to remove your {number} {follower}?", "profile_plural": "profiles", "profile_singular": "profile", "remove_followers": "Remove followers", "saved_post": "Saved post", "post_removed": "Post removed", "post_removed_f_y_col": "Post removed from you collections", "no_published_jobs": "No published jobs", "NUMBER_IS_NOT_ALLOW": "Number is not allowed", "signed_up_within_your_invitation": "signed up within your invitation", "email_invitations_because_they_are_lobox_users": "email invitations, because they are already lobox users", "invitations_lower": "invitations", "lobox_users": "Lobox users", "acc_sync_failed": "Account sync failed", "try_again_to_add_your_google_account_make_sure_checkboxes": "Try again to add your Google account and make sure you checked all of the checkboxes", "please_check_all_boxes_google_sync": "Please, don't forget to check each checkbox during your Google account adding process. By doing so, you'll enable us to access and display your contacts seamlessly", "view_people": "View people", "INVALID_FORMAT_ONLY_LETTERS_EXCEPTION": "Number is not allowed", "we_w_l_t_hear_feedback": "We would love to hear your feedback if you think we can improve", "yr_d_inc_f_p_w_lost": "Your data, including followers and page associations, will be permanently lost", "this_ac_cnt_und": "This action cannot be undone!", "a_24_h_verfi_lin_email": "A 24 hour valid verification link has been sent to the email below to complete the process of removing your page", "24_h_valid_sent_f_p_d": "24 hour valid link sent to this email for page deletion:", "we_excluded": "We excluded", "invited_users": "Invite users", "over_lower": "over", "file_types_for_bulk_email": "Accepted file types: .txt, .csv, .xls, .xlsx, .doc, .docx, .pdf", "BulkInvitationFileLoadingException.title": "Uplaod to load", "BulkInvitationFileLoadingException.message": "It seems the file contets are incorrect. Please try again", "BulkInvitationFileNotFormatException.title": "Wrong file format", "BulkInvitationFileNotFormatException.message": "We can't upload the file because it's format is incorrect", "(optional)": "(optional)", "share_resume": "Share resume", "some_name_resume": "{user}'s resume", "this_content_is_n_access": "This content is no longer accessible", "associated_people": "Associated people", "the_pr_th_a_f_y_b": "The profiles that are following you but you don't follow them", "workspace_type": "Workplace type", "there_is_n_p_s": "There is no previous step", "there_is_n_n_s": "There is no next step", "user_agreement_for_business": "User Agreement for Business", "cookie_policy": "<PERSON><PERSON>", "copyright_and_intellectual_property_policy": "Copyright and Intellectual Property Policy", "california_consumer_privacy_rights_notice": "California Consumer Privacy Rights Notice", "republic_of_korea": "Republic of Korea", "brazilian_general_data_protection_law": "Brazilian General Data Protection Law", "community_policies": "Community Policies", "advertising_policies": "Advertising Policies", "legal": "Legal", "coming_3dot": "Coming up", "w_r_w_o_it": "We are working on it to bring it to live", "business_contact": "Business contact", "w_al_c_a_y_n_y_c_c_i_p": "We always care about your needs. You can either put your contact information here or email us and Lobox will be in touch", "interested_i_b_col_q": "Interested in business collaborations or have questions?", "one_p_one_e": "One Platform, One Ecosystem", "create_account": "Create account", "h_an_acc": "Have an account?", "by_sign_y_a_t": "By signing up, you agree to the <b>{name}</b>, <b>{name}</b> and <b>{name}</b>", "terms_of_serv": "Terms of Service", "email_verification": "Email verification", "verification_sent": "Verification sent", "confirm_t_e_y_u": "Confirm the email you used in signup", "wrong_email": "Wrong email?", "mk_s_t_c_t_s_j_any_e": "Make sure to check the spam or junk folder. If you didn’t receive any email", "click_resent": "<PERSON><PERSON>", "occupation": "Occupation", "personal_ino": "Personal info", "last_name": "Last name", "y_h_an_acc": "You don't have any account?", "forget_password": "Forgot password?", "y_c_r_y_pass": "You can reset your password within sent email", "set_new_pass": "Set new password", "elevate_y_m_b_prop": "Elevate your mission by propelling your page to new heights", "schedule_sync_succ": "Schedule, Sync, Succeed", "navigate_y_c_p_w_opp": "Navigate your career path with opportunities that are the best fit for you", "reset_pass_email_s_a": "Reset password email sent again", "forget_pass_email_s_a": "Forget password email sent again", "verification_code_s_a": "verification code sent again", "your_profile": "Your profile", "user_data": "User Data", "premium_services": "Premium Services", "copy_right_text": "Copyright", "too_weak": "Too weak", "try_stronger": "Try stronger", "strong": "Strong", "cookies_give_y_a_per_exp": "Cookies give you a personalized experience", "we_u_cookies_on_our_web_desc": "We use cookies on our website and services to give you the most personalized experience by remembering your preferences and repeat visits", "u_c_f_m_in_i_our": "You can find more information in our <b>{name}</b> page or By clicking “Accept all”, you contest to the use of all the cookies or you can <b>{name}</b>", "only_essentials": "Only essentials", "accept_all": "Accept all", "manage_cookies": "Manage cookies", "see_cookies": "See cookies", "cookie_policy_sm": "<PERSON><PERSON>", "allow_cookies": "Allow cookies", "es_cookies": "Essential Cookies", "these_cookies_ar_nec_f_t_web": "These cookies are necessary for the website to function properly. They enable basic features such as page navigation and access to secure areas", "third_party": "Third Party", "used_t_rec_an_count_t_n": "Used to recognize and count the number of visitors and to see how visitors move around platform when they are using it. This let Lobox to improve the way the it works, for example by helping to ensure that users are easily finding what they are looking for", "posts_lower": "posts", "ResourceNotFoundRestApiException.title": "No longer available", "ResourceNotFoundRestApiException.message": "Job does not exist anymore", "JobNotFoundException.title": "No longer available", "JobNotFoundException.message": "Job does not exist anymore", "UnauthorizedRestApiException.title": "Unauthorized action", "UnauthorizedRestApiException.message": "You are not allowed to save the job", "recommend_user": "Recommend user", "top_suggestions_lower": "top suggestions", "your_daily_top_suggestions_for_jobs_update": "Your daily top suggestions for jobs updated", "you_have_saved_jobs_however_not_applied_yet": "You have saved jobs however not applied yet", "new_device_logged_in_is_it_you": "A new device logged in from {locationTitle}. If this wasn't you, please check your logged devices", "today_is_persons_birthday_you_can_send_a_message": "Today is {fullname} birthday, You can send a celebration message", "person_commented_under_your_post": "{fullname} commented under your post", "person_and_count_others_commented_under_your_post": "{person} and {count} others commented under your post", "person_shared_your_post": "{fullname} shared your post", "person_and_count_others_shared_your_post": "{fullname} and {count} others shared your post", "we_excluded_count_of_count2_email_invitations_because_they_are_users": "We excluded {count} of {count2} email invitations, because they are already Lobox users", "remember_to_add_your_profile_image_for_better_recognition": "Remember to add your profile image for better recognition by others", "remember_to_add_your_profile_cover_for_better_recognition": "Remember to add your profile cover for better recognition by others", "person_started_following_you": "{fullname} started following you", "expand_your_skills_section_by_adding_at_least_one_more_skill": "Expand your skills section by adding at least one more skill to your profile", "person_liked_another_person_comment_under_a_post": "{person} {action} {person2} comment under a post", "person_and_others_reacted_to_another_person_comment_under_a_post": "{person} and {count} others reacted to {person2} comment under a post", "REACTION_VERB_liked": "liked", "REACTION_VERB_boosted": "boosted", "REACTION_VERB_celebrated": "celebrated", "REACTION_VERB_disliked": "disliked", "REACTION_VERB_not_relevant": "not relevant", "person_liked_your_comment_under_your_post": "{person} {action} your comment under your post", "persond_and_count_others_reacted_to_your_comment_under_your_post": "{person} and {count} others reacted to your comment under your post", "you_may_know_person_from_company_do_you_want_follow": "You may know {person} from {company}. Do you want to follow?", "your_password_has_been_changed_successfully": "Your password has been changed successfully", "password_lower": "password", "enhance_your_profile_by_add_your_profile_sections_list": "Enhance your profile by adding your {listOfProfileSections}", "EMAIL_lower": "email", "PHONE_lower": "phone", "LINK_lower": "link", "EXPERIENCE_lower": "experience", "EDUCATION_lower": "education", "SKILL_lower": "skill", "remember_to_add_your_profile_image_for_better_recognition_by_others": "Remember to add your profile image for better recognition by others", "include_page_sections_list_on_your_profile_for_easy_contact": "Include {listOfPageSections} on your profile for easy contact", "add_information": "Add information", "remember_to_add_your_page_logo_for_better_recognition": "Remember to add your page logo for better recognition by others", "person_mentioned_another_person_in_comment_under_post": "{person} mentioned {person2} in a comment under a post", "person_and_count_others_mentioned_person2_in_the_comments_under_post": "{person} and {count} others mentioned {person2} in the comments under a post", "person_mentioned_another_person_in_under_your_post": "{person} mentioned {person2} under your post", "person_and_count_others_mentioned_person2_under_your_post": "{person} and {count} others mentioned {person2} under your post", "person_mentioned_you_in_comment_under_post": "{person} mentioned you in a comment under a post", "person_and_count_others_mentioned_you_in_the_comments_under_post": "{person} and {count} others mentioned you in the comments under a post", "person_mentioned_you_in_under_your_post": "{person} mentioned you under your post", "person_and_count_others_mentioned_you_under_your_post": "{person} and {count} others mentioned you under your post", "person_mentioned_another_person_in_a_post": "{person} mentioned {person2} in a post", "person_mentioned_you_in_a_post": "{person} mentioned you in a post", "person_and_count_others_mentioned_you_in_a_post": "{person} and {count} others mentioned you in a post", "name_gave_y_role_portal": "{name} gave you the {role} role of {page} portal", "person_replied_to_a_comment_under_your_post": "{person} replied to a comment under your post", "person_and_count_others_replied_to_a_comment_under_your_post": "{person} and {count} others replied to a comment under your post", "person_replied_to_your_comment_under_person2_post": "{person} replied to your comment under {person2}'s post", "person_and_count_others_replied_to_your_comment_under_person2_post": "{person} and {count} others replied to your comment under {person2}'s post", "person_replied_to_your_comment_under_your_post": "{person} replied to your comment under your post", "person_and_count_others_replied_to_your_comment_under_your_post": "{person} and {count} others replied to your comment under your post", "person_commented_under_person2_post": "{person} commented under {person2}'s post", "person_and_count_others_commented_under_person2_post": "{person} and {count} others commented under {person2}'s post", "person_liked_your_comment_under_person2_post": "{person} {action} your comment under {person2}'s post", "persond_and_count_others_reacted_to_your_comment_under_person2_post": "{person} and {count} others reacted to your comment under {person2}'s post", "person_reacted_to_person2_comment_under_your_post": "{person} {action} {person2}'s comment under your post", "persond_and_count_others_reacted_to_person2_comment_under_your_post": "{person} and {count} others reacted to {person2}'s comment under your post", "person_reacted_person2_post": "{person} {action} {person2}'s post", "person_and_count_reacted_to_person2_post": "{person} and {count} others reacted to {person2}'s post", "person_reacted_your_post": "{person} {action} your post", "person_and_count_others_reacted_to_your_post": "{person} and {count} others reacted to your post", "over_count_people_signed_up_with_your_invitation": "Over {count} people signed up within your invitation", "pageName_page_published_successfully": "{pageName} page published successfully", "person_requested_to_follow_you": "{person} requested to follow you", "you_decilined_request": "You declined request", "you_accepted_request": "You accepted request", "person_declined_pageRole_role_request_of_pageName_page": "{person} declined {pageRole} role request of {pagename} page", "person_accepted_pageRole_role_request_of_pageName_page": "{person} accepted {pageRole} role request of {pagename} page", "you_may_know_person_from_pageName_do_you_want_to_follow": "You may know {person} from {pageName}. Do you want to follow?", "person_tagged_you_in_a_post": "{person} tagged you in a post", "count_people_applied_for_pageTitle_jobTitle_job_today": "{count} people applied for {pageTitle}'s {jobTitle} job today", "useful_links": "Useful links", "switch_t_desk": "Switch to Desktop Version", "in_order_use_bus_fea_use_lb": "In order to use Business features, use Lobox Desktop version for now", "go_back": "Go back", "user_profile": "User Profile", "analytics_cookies": "Analytics Cookies", "w_u_analytics_cookies_t_u_h_visit": "We use analytics cookies to understand how visitors interact with our website. This helps us improve the site and tailor it to your needs", "marketing_cookies": "Marketing Cookies", "our_marketing_partners_m_s_cookie": "Our marketing partners may set cookies to track your interactions with their content. These cookies may be used to build a profile of your interests and show you relevant ads on other sites", "collected_data_co_per": "Collected data and cookies preference", "mng_y_data_coll_per": "Manage your data collections preferences", "y_c_change_t_set_i_per_any": "You can change the settings in <b>{name}</b> anytime", "preferences_lower": "preferences", "business_profile": "Business Profiles", "pageTitle_closed_the_jobTitle_job_in_your_saved_job": "{pageTitle} closed the {jobTitle} job in your saved job", "jobTitle_jobs_found_in_locationTitle_based_on_your_job_alerts": "{jobTitle} jobs found in {locationTitle} based on your job alerts", "person_accepted_meetingTitle_meeting": "{person} accepted {meeting<PERSON><PERSON><PERSON>} meeting", "person_removed_taskTitle_task": "{person} removed {taskTitle} task", "person_updated_taskTitle_task": "{person} updated {taskTitle} task", "person_declined_meetingTitle_meeting": "{person} declined {meeting<PERSON><PERSON><PERSON>} meeting", "person_updated_meetingTitle_meeting": "{person} updated {meetingTitle} meeting", "person_removed_meetingTitle_meeting": "{person} removed {meeting<PERSON><PERSON><PERSON>} meeting", "meetingTitle_meeting_is_going_to_start_in_amountOfTime_minutes": "{meetingTitle} meeting is going to start in {amountOfTime} minutes", "person_accepted_your_following_request": "{person} accepted your following request", "person_liked_your_comment_under_a_post": "{person} {action} your comment under a post", "persond_and_count_others_reacted_to_your_comment_under_a_post": "{person} and {count} others reacted to your comment under a post", "calendarName_not_yet_synchronized_enable_it": "{calendarName} not yet synchronized. Enable integration for seamless calendar management", "multiple_calendar_found_integrate_additional_calendars": "Multiple calendars found. Integrate additional calendars for comprehensive scheduling", "EDITOR": "Editor", "RECRUITER": "Rec<PERSON>er", "SERVICE": "Service", "CAMPAIGN": "Campaign", "SALES": "Sales", "create_connections_and_build_friendships_people_you_may_know": "Create connections and build friendships, Here are people you may know!", "people_you_may_know": "people you may know!", "stay_updated_and_engage_with_businesses_here_are_pages_you_may_know": "Stay updated and engage with businesses, Here are pages you may know!", "pages_you_may_know": "pages you may know!", "person_requested_you_to_be_pageRole_of_pageName_page": "{person} requested you to be {pageRole} of {pageName} page", "name_gave_y_role_page": "{name} gave you the {role} role of {page} page", "person_replied_to_a_comment_you_mentioned_in": "{person} replied to a comment you mentioned in", "no_notification": "No notification", "there_are_no_notifications_to_show": "There are no notifications to show", "person_and_count_others_replied_to_a_comment_you_mentioned_in": "{person} and {count} others replied to a comment you mentioned in", "view_pages": "View pages", "associated": "Associated", "no_associated_people": "No associated people", "yr_associated_p_t_t_p": "Your associated people to this page", "pg_delete_failed_expired": "Page deletion failed due to expired link", "link_expired": "Link expired", "y_w_lose_all_y_f_m_p_r": "You will lose all your Followers/Followings, messages, pages, recommendations, and everything else", "note": "Note", "t_f_p_w_b_deleted_w_a": "The following pages will be deleted along with your account", "r_y_s_y_w_d_ac_ass_pg": "Are you sure you want to delete your account with its associated pages?", "w_s_t_b_re_d_ret": "We suggest taking a break and upon returning your data is retrievable", "it_t_u_two_weeks_d_all": "It takes up to two weeks to delete all of your data from our servers", "acc_del_fai_d_t_ex_l": "account deletion failed due to expired link", "y_pa_deleted_successfully": "Your page deleted successfully", "y_acc_deleted_successfully": "Your account deleted successfully", "account_deleted": "Account deleted", "delete_y_p_per_undone": "Delete your page permanently. (This action cannot be undone!)", "email_successfully_sent": "<PERSON><PERSON> was sent successfully", "operation_failed": "Operation failed!", "could_not_send_the_email": "Could not send the email", "could_not_remove_notification": "Could not remove notification", "you_have_unsubscribed": "You’ve unsubscribed", "unsubscribe_email_message": "You’ll no longer receive these type of emails from Lobox", "manage_email_preferences": "Manage email preferences", "unsubscribed_by_accident": "Unsubscribed by accident?", "subscribe_again": "Subscribe again", "understood_welcome_back": "Understood, Welcome back!", "subscribed_again_message": "You’ve subscribed again to receive these email types", "switching_to_lobox_portal": "Switching to Lobox {portal}", "new_message": "New message", "select_or_create": "Select or create", "yr_bl_profiles": "Your blocked profiles", "sound_notif": "Sound notification", "mute_sound_notif": "Mute sound notifications", "yr_started_msg": "Your starred messages", "yr_archived_conv": "Your archived conversations", "online_status": "Online status", "yr_online_status": "Your online status", "allow_req": "Allow requests", "allow_strangers_t_msg": "Allow strangers to message you", "block_user": "Block user", "r_y_s_y_w_t_d_conv_a_lose_cont": "Are you sure you want to delete conversation and lose all the content in it?", "conversation_deleted_success": "Conversation deleted successfully", "replying_to_name": "Replying to <b>{name}</b>", "create_group": "Create group", "connect_with_y_f_a_t": "Connect with your friends all together", "no_message": "No message", "no_msg_h_b_sent_o_reciv": "No message has been sent or received yet", "add_member": "Add members", "group_name": "Group name", "add_group_image": "Add group image", "replied_to_name": "Replied to <b>{name}</b>", "search_a_w_i_c": "Search a word in conversation", "edited_at": "Edited at", "forward_from_name": "Forwarded from <b>{name}</b>", "job_application": "Job application", "no_archive_found": "No archive found", "y_c_archive_con_t_k": "You can archive conversations to keep them in your library", "removed_success": "Removed successfully", "conv_removed_f_arch": "Conversation removed from archive", "msg_removed_f_stared": "Message removed from starred", "archived_success": "Archived successfully", "conv_moved_t_arch": "Conversation moved to archive", "no_stared_message": "No starred messages", "y_c_start_msg_keep_them": "You can star messages to keep them in your messenger library", "stared_message": "Stared message", "group_created": "Group created", "yr_g_c_succ": "Your group created successfully", "member_removed": "Member removed", "member_removed_f_g": "Member removed from group", "admin_removed": "Admin removed", "member_access_donw_t_m": "Member access downgraded to member", "admin_added": "Admin added", "member_access_upgraded_t_a": "Member access upgraded to admin", "group_updated": "Group updated", "yr_g_u_succ": "Your group updated successfully", "JOB_SHARED_PREVIEW": "Job shared", "TEXT_SHARED_PREVIEW": "Text shared", "POST_SHARED_PREVIEW": "Post shared", "FILE_SHARED_PREVIEW": "File shared", "EVENT_SHARED_PREVIEW": "Event shared", "TASK_SHARED_PREVIEW": "Task shared", "IMAGE_SHARED_PREVIEW": "Image shared", "PROFILE_SHARED_PREVIEW": "Profile shared", "MEETING_SHARED_PREVIEW": "Meeting shared", "BIRTHDAY_SHARED_PREVIEW": "Birthday shared", "AVAILABILITY_SHARED_PREVIEW": "Availability shared", "RECOMMENDATION_SHARED_PREVIEW": "Recommendation shared", "IS_ROOM_ACT_MESSAGE_SHARED_PREVIEW": "Room shared", "HOLIDAY_SHARED_PREVIEW": "Holiday shared", "view_recommendation": "View recommendation", "yr_msg_sent_succ": "Your message sent successfully", "edit_group_image": "Edit group image", "search_participants": "Search participants", "y_r_n_able_t_s_msg": "You are not able to send a message", "un_archive": "Unarchived", "articles_notifications": "Articles notifications", "club_notifications": "Club notifications", "general_notifications": "General notifications", "job_notifications": "Job notifications", "news_notifications": "News notifications", "page_notifications": "Page notifications", "people_notifications": "People notifications", "post_notifications": "Post notifications", "schedule_notifications": "Schedule notifications", "search_notifications": "Search notifications", "notification_settings_message": "Push, In-app and Email", "allow_general_notifications": "Allow general notifications", "avatar_notification": "Avatar notification", "cover_notification": "Cover notification", "create_page_notification": "Create page notification", "profile_completion_notification": "Profile completion notification", "allow_post_notifications": "Allow post notifications", "post_reaction_notification": "Post reaction", "post_comment_notification": "Post comment", "comment_reaction_notification": "Comment reaction", "comment_reply_notification": "Comment reply", "comment_mention_notification": "Comment mention", "post_mention_notification": "Post mention", "post_tag_notification": "Post tag", "post_share_notification": "Post share", "allow_job_notifications": "Allow job notifications", "job_application_status_notification": "Job application status notification", "closed_job_notification": "Closed job notification", "job_alert_notification": "Job alert notification", "top_suggestion_job_notification": "Top suggestion job notification", "daily_job_recommendation_notification": "Daily job recommendation notification", "application_reminder": "Application reminder", "job_candidates_notification": "Job candidates notification", "job_application_notification": "Job application notification", "allow_search_notifications": "Allow search notifications", "profile_show_up_notification": "Profile show up notification", "allow_people_notifications": "Allow people notifications", "people_you_may_know_notification": "People you may know notification", "new_people_from_company_notification": "New people from company notification", "new_people_from_school_notification": "New people from school notification", "following_people_birthday_notification": "Following people birthday notification", "invite_people_notification": "Invite people notification", "allow_page_notifications": "Allow page notifications", "new_follower_notification": "New follower notification", "post_update_notification": "Post update notification", "webinar_notification": "Webinar notification", "pages_you_may_know_notification": "Pages you may know notification", "publishment_status_notification": "Publishment status notification", "new_role_notification": "New role notification", "ownership_transfer_notification": "Ownership transfer notification", "premium_plan_notification": "Premium plan notification", "update_and_engagement_notification": "Update and engagement notification", "performance_and_insights_notification": "Performance and insights notification", "engagement_tips_notification": "Engagement tips notification", "promotional_tools_notification": "Promotional tools notification", "event_promotion_notification": "Event promotion notification", "milestone_achievement_notification": "Milestone achievement notification", "allow_club_notifications": "Allow club notifications", "scheduled_post_notification": "Scheduled post notification", "new_member_notification": "New member notification", "reach_and_impression_notification": "Reach and impression notification", "post_request_notification": "Post request notification", "post_alert_notification": "Post alert notification", "member_modification_notification": "Member modification notification", "club_share_notification": "Club share notification", "club_permission_notification": "Club permission notification", "post_publishment_notification": "Post publishment notification", "club_invitation_notification": "Club invitation notification", "following_post_share_notification": "Following post share notification", "allow_schedule_notifications": "Allow schedule notifications", "meeting_status_notification": "Meeting status notification", "meeting_invitation_notification": "Meeting invitation notification", "meeting_response_status_notification": "Meeting response status notification", "meeting_reminder_notification": "Meeting reminder notification", "meeting_booked_notification": "Availability meeting notification", "task_status_notification": "Task status notification", "task_comment_notification": "Task comment notification", "reminder_notification": "Reminder notification", "calendar_integration_notification": "Calendar integration notification", "allow_articles_notifications": "Allow articles notifications", "scheduled_articles_notification": "Scheduled articles notification", "articles_publishment_notification": "Articles publishment notification", "article_comment_notification": "Article comment notification", "article_reactions_notification": "Article reactions notification", "article_insights_notification": "Article insights notification", "article_share_notification": "Article share notification", "article_report_notification": "Article report notification", "article_status_notification": "Article status notification", "comment_report_notification": "Comment report notification", "allow_news_notifications": "Allow news notifications", "scheduled_news_notification": "Scheduled news notification", "news_publishment_notification": "News publishment notification", "news_comment_notification": "News comment notification", "news_reaction_notification": "News reaction notification", "news_insights_notification": "News insights notification", "news_share_notification": "News share notification", "news_report_notification": "News report notification", "news_status_notification": "News status notification", "business_catalogs": "Business catalogs", "catalog": "Catalog", "explore_the_plat_feat": "Explore the platform's features", "explore_the_bus_plat_feat": "Explore the business features", "portal_catalog": "{name} catalog", "facilitate_a_co_on_obj": "Facilitate and collaborate on objectives", "portal_roles": "Portal roles", "mng_portal_role_f_s_team": "Manage {name} roles for smooth teamwork", "recruit_revolu": "Recruitment Revolution", "intuitive_portal_f_t_can_comp": "Intuitive portal for top-match candidates and comprehensive hiring tools", "streamline_didig_tal_ac_t_boost_p": "Streamline digital talent acquisition to boost productivity and efficiency", "clubs": "Clubs", "set_reminders_stay_on_track": "Set reminders, stay on track effortlessly", "organize_tasks_manage_goals": "Organize tasks, manage daily goals efficiently", "connect_lobox_business_team": "Effortlessly connect with Lobox business team", "get_24_7_help_quickly": "Get 24/7 help with any issue quickly", "todos": "Todos", "to_do": "To do", "capture_ideas_organize_thoughts": "Capture ideas, organize thoughts effectively", "discover_premium_plans_portal": "Discover the premium plans for {portal}", "view_billing_summary_portal": "View detailed billing summary for {portal}", "plans": "Plans", "billings": "<PERSON><PERSON>", "join_communities_build_connections": "Join communities, build connections effortlessly", "inform_audience_latest_happenings": "Inform your audience with latest happenings", "delve_topics_offer_insights_readers": "Delve into topics, offer insights to readers", "facilitate_collaborate_objectives": "Facilitate and collaborate on objectives", "club": "Club", "organize_streamline_business_operations": "Organize and streamline business operations", "manage_progress_business_practices": "Manage every progress of business practices", "dashboard": "Analytics", "pipelines": "Pipelines", "projects": "Projects", "project": "Project", "evaluate_assess_qualifications": "Evaluate and assess qualifications thoroughly", "candidate": "Candidate", "there_is_n_ticket": "There is no ticket", "new_ticket": "New ticket", "create_a_t_t_r_o": "Create a ticket to reach our support team", "transfer_ownership": "Transfer ownership", "members": "Members", "only_u_w_mem_admin_auth_c_s": "Only users with membership or admin authority can be selected to transfer ownership", "search_users": "Search users", "portal": "Portal", "role": "Role", "mgn_owner_page_role": "Manage ownership and page roles", "accessibility": "Accessibility", "remove_accessibility": "Remove accessibility", "transform": "Transform", "more_info": "More info", "service_transformation": "Service Transformation", "platform_showcase_services_manage_clients": "Platform for professionals to showcase services and manage clients efficiently", "streamlines_client_interaction_service": "Streamlines client interaction and service, setting a new digital standard", "marketing_revolution": "Marketing Revolution", "uses_marketing_tools_revolutionize_strategies": "Uses state-of-the-art marketing tools to revolutionize campaign strategies", "enhances_campaign_management_tools": "Enhances campaign management with next-generation marketing tools", "sales_transformation": "Sales Transformation", "sell_products_reach_more_buyers": "Sell your products effortlessly and reach more buyers with our marketplace", "transforms_sales_advanced_tools": "Transforms sales with advanced tools for streamlined automation and strategies", "contact_with_team": "Connect with team", "admins": "Admins", "user": "User", "service": "Service", "campaign": "Campaign", "sales": "Sales", "EDITOR_HEAD_label": "Head", "EDITOR_EDITOR_MANAGER_label": "Editor Manager", "EDITOR_EDITOR_label": "Editor", "EDITOR_MEDIA_MANAGER_label": "Media Manager", "EDITOR_MEDIA_DESIGNER_label": "Media Designer", "EDITOR_COMMUNITY_MANAGER_label": "Community Manager", "EDITOR_COMMUNITY_COORDINATOR_label": "Community Coordinator", "EDITOR_ANALYST_label": "Analyst", "RECRUITER_HEAD_label": "Head", "RECRUITER_RECRUITER_MANAGER_label": "Recruiter Manager", "RECRUITER_RECRUITER_label": "Rec<PERSON>er", "RECRUITER_HIRING_MANAGER_label": "Hiring Manager", "RECRUITER_EXPERT_MANAGER_label": "Expert Manager", "RECRUITER_EXPERT_label": "Expert", "RECRUITER_ANALYST_label": "Analyst", "CAMPAIGN_ANALYST_label": "Analyst", "CAMPAIGN_HEAD_label": "Head", "CAMPAIGN_MARKETING_MANAGER_label": "Marketing Manager", "CAMPAIGN_MARKETING_SPECIALIST_label": "Marketing Specialist", "CAMPAIGN_ADS_MANAGER_label": "Ads Manager", "CAMPAIGN_ADS_SPECIALIST_label": "Ads Specialist", "SERVICE_HEAD_label": "Head", "SERVICE_SERVICE_MANAGER_label": "Service Manager", "SERVICE_SERVICE_SPECIALIST_label": "Service Specialist", "SERVICE_LEGAL_MANAGER_label": "Legal Manager", "SERVICE_LEGAL_SPECIALIST_label": "Legal Specialist", "SERVICE_SUPPORT_MANAGER_label": "Support Manager", "SERVICE_SUPPORT_SPECIALIST_label": "Support Specialist", "SERVICE_ANALYST_label": "Analyst", "SALES_HEAD_label": "Head", "SALES_SALES_MANAGER_label": "Sales Manager", "SALES_SALES_SPECIALIST_label": "Sales Specialist", "SALES_FINANCE_MANAGER_label": "Finance Manager", "SALES_FINANCE_SPECIALIST_label": "Finance Specialist", "SALES_ANALYST_label": "Analyst", "revoke_access": "Revoke access", "error": "Error", "transferring_own_is_irr_p_v": "Transferring ownership is irreversible. Please verify the recipient you are choosing. You will be logged out from this profile", "pipeline": "Pipeline", "create_project": "Create project", "ads": "Ads", "campaigns": "Campaigns", "clients": "Clients", "customers": "Customers", "get_business": "Get business", "user_catalog": "User catalog", "business_sections": "Business sections", "mng_yr_availability_sch_w_e": "Manage your availability and schedule with ease", "t_cr_a_j_g_t_re_tool_acc_vi_link": "To create a page, go to the Re<PERSON>ruiter tool, accessible via the links provided below", "t_c_a_j_t_n_t_f_c_a_p_rec_tool": "To create a job, you need to first create a page. Once the page is created, you can go to the Recruiter tool to proceed with creating the job", "available_time": "Available time", "available_hours": "Available hours", "these_your_available_hours_others_schedule_meeting": "These are your available hours for others to schedule a meeting with you", "promote_ini_eng_au_wel": "Promote initiatives, engage audience well", "promote_pro_att_aud_eff": "Promote products, attract audience effectively", "support_cus_add_nee_prom": "Support customers, address needs promptly", "client": "Client", "boost_sales_th_ef_stre": "Boost sales through effective strategies", "customer": "Customer", "coming_up": "Coming up", "a_p_r_t_b_i_sys_to_c_bus": "A page refers to a business in the system.To create a business, you need to create a page first", "empty_project_title": "No project found", "empty_project_description": "Let’s get you a project first to create a job for it", "choose_projects": "Choose projects", "choose_projects_description": "Projects help you to organize your jobs and their settings", "one_year": "One year", "six_months": "Six months", "three_months": "Three months", "response_time": "Response time", "number_of_hires": "Number of hires", "schedule_publish": "Schedule publish", "expiry_date": "Expiry date", "allow_apply_globally": "Allow to apply globally", "external_link": "External link", "add_job_link": "Add external job link", "website_link": "Website link", "flexible": "Flexible", "immediate": "Immediate", "in_1_day": "In 1 day", "in_1_month": "In 1 month", "in_1_week": "In 1 week", "in_1_year": "In 1 year", "in_2_days": "In 2 days", "in_2_weeks": "In 2 weeks", "in_3_days": "In 3 days", "management": "Management", "priority": "Priority", "critical": "Critical", "high": "High", "medium": "Medium", "low": "Low", "advance_feature": "Advance feature", "advance_feature_tootip": "You can advanced features later in job details", "requirements_description": "Setup what is needed for the job", "benefits_description": "Offer the benefits of the position to job seekers", "pipelines_description": "Customize your pipelines of hiring with automation features", "auto_reply": "Auto reply", "auto_reply_description": "Set automatic replies to applicants", "application_form": "Application form", "application_form_description": "Customize form and add questions", "email_description": "Description", "phone_description": "Obligate applicant to enter phone number", "resume_description": "Obligate applicant to to upload resume", "cover_letter_description": "Obligate applicant to send a cover letter", "modified": "Modified", "add_comment": "Add comment", "add_question": "Add question", "obligatory_fields": "Obligatory fields", "minimum_degree": "Minimum degree", "contract_duration": "Contract duration", "willingness_for_travel": "Willingness for travel", "specification": "Specification", "shift_hours": "Shift hours", "hours_per_day": "Hours per day", "hours_per_week": "Hours per week", "work_authorization": "Work authorization", "select_authorization": "Select authorization", "skills_minimum_items": "You need to add at least 3 and max 10 skills", "financial_metrics": "Financial Metrics", "tax_term": "Tax term", "markup": "<PERSON><PERSON>", "add_benefits": "Add benefits", "applicant_track": "Applicant track", "applicant_track_desc": "Allow applicant to track their status changes in this stage", "delete_stage": "Delete stage", "delete_stage_desc": "Are you sure you want to delete stage?", "add_stage": "Add stage", "working_days_in_a_week": "Working days in a week", "required_hours_week": "Required hours/week", "project_created_successfully": "Project successfully created", "MaximumRolePerPortalExceedException": "A user can be assigned a maximum of 2 roles per portal", "and_num_more": "and {num} more", "send_email": "Send email", "drag_a_d_y_f_name_name": "Drag & drop your file <b>{name}</b> <b>{name}</b>", "acc_f_for_attachment": "Accepted file types: .jpg, .jpeg, .png, and .pdf only, max 20MB", "drop_yr_files_t_up_them": "Drop your files to upload them", "upload_another_file": "Upload another file", "wrong_format": "Wrong format", "attachment": "Attachment", "large_file_size": "Large file size", "max_number_of_attach_files": "You can attach maximum 10 files", "member": "Member", "add_availability": "Add availability", "link_to_jobs": "Link to jobs", "link_job": "<PERSON> job", "link_to_jobs_desc": "You can link jobs to this project once the project has been created", "link_job_title": "Here’s a recommendation!", "link_job_description": "You can link jobs to your project and make them more organized", "job_linked": "Job linked", "job_linked_successfully": "Your job(s) linked successfully", "primary_contact": "Primary contact", "edit_assignee": "Edit assignee", "unhide": "Unhide", "services": "Services", "auto_reply_desc": "Each stage links to multiple templates to notifies applicants with status change", "stages": "Stages", "onboarding_stages": "Onboarding stages", "not_set_yet": "Not set yet", "message_template": "Message template ({title})", "template_num": "Template {num}", "message_title": "Message title", "have_fu_msg": "Have follow-up message", "fu_after": "Follow up after", "_3_DAYS": "3 days", "_5_DAYS": "5 Days", "_7_DAYS": "7 Days", "_14_DAYS": "14 Days", "APPLICANT_NAME": "Applicant's Name", "PAGE_NAME": "Page Name", "JOB_TITLE": "Job Title", "RECRUITER_NAME": "Recruiter Name", "create_job_unbulish_alert": "Job saved in your unpublished jobs for later use too", "t_j_l_h_b_c_t_y_c": "The job id has been copied to your clipboard", "interview": "Interview", "offered": "Offered", "onboarded": "Onboarded", "create_job_description": "But you have no jobs to link", "yr_online_status_updated_cuss": "Your online status updated successfully", "on_site_location": "On site location", "meeting_link_15_minutes": "The meeting link will be created 15 minutes before the meeting starts", "send_emoji_failed": "Send emoji failed", "expired_link": "Expired link", "the_shared_content_no_longer_exi": "The shared content no longer exists", "reviews": "Reviews", "no_applicant": "No applicant found", "no_applicant_desc": "No applicants have been added for this job", "applicant_stage_updated": "Applicant stage updated successfully", "no_candidate": "No candidate found", "no_candidate_desc": "No candidates have been added for this job", "candidate_stage_updated": "Candidate stage updated successfully", "recruitment_team": "Recruitment team", "view_candidate": "View candidate", "pipeline_changed_activity": "<b>{name}</b> changed <b>{name}</b> status", "user_applied_activity": "<b>{name}</b> applied", "user_withdraw_activity": "<b>{name}</b> withdraw from application", "review_added_activity": "<b>{name}</b> wrote a review for <b>{name}</b>", "review_modified_activity": "<b>{name}</b> modified <b>{name}</b> review", "review_removed_activity": "<b>{name}</b> removed <b>{name}</b> review", "DuplicatePortalAccessException": "You have already assigned this role to the user", "we_r_sorry_t_s_y_r_closing_acc": "We are sorry to see you go, Are you sure about closing your account?", "but_people_who_al_h_mes_mi_s_msg_t_y": "But people who already have messages with you might send messages to you and you can get them when you activate your account again", "authentication": "Authentication", "to_use_schedule_login": "To use the scheduling features in Lobox, you need to join our platform", "sign_up_or_login": "Sign up or log in to proceed", "no_review": "No review found", "no_review_desc": "No reviews have been added for this job", "empty_projects": "You don't have any project", "empty_projects_description": "Let’s get you a project to start recruiment journey", "add_jobs": "Add jobs", "add_candidates": "Add candidates", "no_jobs": "No job found", "no_jobs_desc": "There are no jobs, try to create one", "no_todos": "No todo found", "no_todos_desc": "No todos have been added for this project", "host": "Host", "no_meetings": "No meeting found", "no_past_meeting_desc": "No past meetings have been found for this project", "no_upcoming_meeting_desc": "No upcoming meetings have been found for this project", "no_desc_added": "No description added", "no_link_attached": "No links attached", "no_files_attached": "No files attached", "connected_jobs": "Connected jobs", "project_owner": "Project owner", "t_p_l_h_b_c_t_y_c": "The project id has been copied to your clipboard", "project_id": "Project Id", "created_on": "Created on", "show_completed_Todos": "Show completed todos", "no_activities": "No activity found", "no_activities_desc": "No activities have been recorded for this project", "job_status_changed": "<b>{name}</b> changed <b>{job}</b> job status", "latest_link": "Latest link", "job_priority_changed": "<b>{name}</b> changed <b>{name}</b> job priority", "activity_project_updated": "<b>{name}</b> updated <b>{project}</b> project", "activity_assignee_added": "<b>{name}</b> added <b>{name}</b> as assignee", "activity_assignee_removed": "<b>{name}</b> removed <b>{name}</b> as assignee", "activity_job_linked": "<b>{name}</b> linked <b>{job}</b> to this project", "activity_job_unlinked": "<b>{name}</b> unlinked <b>{job}</b> from this project", "activity_todo_added": "<b>{name}</b> wrote a todo for <b>{name}</b>", "view_job": "View job", "job_and_location": "<b>{job}</b> in <b>{loc}</b>", "removed_job_status": "<b>{name}</b> changed <b>{job}</b> status", "removed_job": "Removed job", "guest": "Guest", "remember_to_add_your_page_header_for_better_recognition": "Remember to add your page header for better recognition by others", "provide_assis_mee_need_eff": "Provide assistance, meet needs effectively", "job_ques_ts_desc": "The total score for all questions will be displayed in the top input field", "total_score": "Total score", "text_input": "Text input", "single_select": "Single select", "multi_select": "Multi select", "numeric": "Numeric", "dropdown": "Dropdown", "must_have": "Must-have", "question_score": "Question score", "hint": "Hint", "options": "Options", "availability_not_found": "Availability not found", "availability_not_found_desc": "This availability no longer exists", "view_availability": "View availability", "not_found_404_error_msg": "Page not found. Please check the URL and try again, or go back to the homepage", "see_posts": "See posts", "person_created_taskTitle_task": "{person} created {taskTitle} task", "y_d_h_a_s_p_t_sh": "You do not have any saved posts to show yet", "job_preferences": "Job preferences", "firstName": "First name", "lastName": "Last name", "other_profile_url": "Other online profile URL", "cell_number": "Cell number", "home_number": "Home number", "work_number": "Work number", "skype_id": "Skype ID", "example_helper": "Example: {sample}", "relocation": "Relocation", "preferred_location": "Preferred location", "age_range": "Age range", "under_18": "Under 18", "race_ethnicity": "Race/Ethnicity", "race_WHITE": "White/Caucasian", "race_ASIAN": "Asian/Pacific Islander", "race_HISPANIC": "Hispanic/Latino", "race_NATIVE_AMERICAN": "Native American/Alaskan Native", "race_MIDDLE_EASTERN": "Middle Eastern/North African", "race_BLACK": "Black/African American", "top_secret": "Top secret", "CLEAN_RECORD": "No Record", "TRAFFIC_VIOLATIONS": "Traffic Violations", "EXPUNGED_RECORDS": "Expunged Record", "FELONIES": "Felonies", "COMPLETED_PROBATION": "Complete Probation", "MISDEMEANORS": "Misdemeanors", "UNDER_INVESTIGATION": "Under Investigation", "veteran_status": "Veteran status", "yes_iam_veteran": "Yes, I am a veteran", "no_iam_n_veteran": "No, I am not a veteran", "candidate_status": "Candidate Status", "open_to_work": "Open to work", "not_open_to_work": "Not open to work", "disability": "Disability", "yes_i_have_disability": "Yes, I have some disability", "no_i_have_n_disability": "No, I do not have a disability", "social_security_number": "Social security number", "usa_ssn_social_security_number": "United States - SSN (Social Secruity Number)", "id_details": "ID details", "id_type": "ID type", "additional_note": "Additional note", "source": "Source", "work_history": "Work History", "edit_education": "Edit education", "recruiter_data": "Recruiter data", "yes": "Yes", "document": "Document", "create_candidate": "Create candidate", "modify_candidate": "Modify candidate", "candidate_created": "Candidate created", "candidate_created_message": "Your candidate created successfully", "candidate_modified": "Candidate modified", "candidate_modified_message": "Your candidate modified successfully", "professional_background": "Professional background", "candidate_information": "Candidate information", "social_information": "Social information", "social_profiles": "Social profiles", "social_acc_n_comm": "Social accounts and communications", "background_information": "Background information", "background_status": "Background status", "clearance_type": "Clearance type", "preferences_n_benefits": "Preferences and benefits", "willing_to_travel": "Willing to travel", "candidate_job_pref_n_salary": "Candidate job preferences and salary expectations", "expected_pay": "Expected pay", "notice_period": "Notice period", "IMMEDIATELY": "Immediately", "custom": "Custom", "full_address": "Full address", "male": "Male", "female": "Female", "prefer_not_to_say": "Prefer not to say", "workspace": "Workplace", "legal_status": "Legal status", "work_authorization_expiry": "Work authorization expiry", "visa_held_by": "Visa held by", "referred_by": "Referred by", "referral_information": "Referral information", "referral_current_company": "Referral current company", "referral_employee_email": "Referral employee email", "referral_contact_number": "Referral contact number", "referring_social_url": "Referring social URL", "link_candid_to_jobs_desc": "You can submit this candidate to jobs after the candidate has been added or created", "jbs_NAUKRI": "<PERSON><PERSON><PERSON>", "jbs_INDEED": "Indeed", "jbs_FACEBOOK": "Facebook", "jbs_GLASSDOOR": "Glassdoor", "jbs_LINKED_IN": "Linkedin", "jbs_CAMPUS_PORTAL": "Campus portal", "jbs_POST_JOB_FREE": "Post job free", "jbs_EMPLOYEE_REFERRAL": "Employee referral", "jbs_WINDOWS_PLUGIN": "Windows plugin", "jbs_PASSIVE_SOURCING": "Passive sourcing", "jbs_ADZUNA": "<PERSON><PERSON><PERSON>", "jbs_TWITTER": "Twitter", "jbs_RESUME_INBOX": "Resume inbox", "jbs_WALK_IN": "Walk in", "jbs_OTHERS": "Others", "jbs_JOBSITE": "Jobsite", "jbs_DICE": "<PERSON><PERSON>", "jbs_ZIPRECRUITER": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jbs_CRAIGLIST": "Craiglist", "jbs_OFFICE_365": "Office 365", "jbs_TECH_FETCH": "Tech fetch", "jbs_EMPLOYMENT_AGENCY": "Employment agency", "jbs_STACK_OVERFLOW": "Stack overflow", "jbs_CAREER_BUILDER": "Career builder", "jbs_INTERNAL_JOB_POSTING": "Internal job posting", "jbs_JOB_FAIR": "Job fair", "jbs_OUTLOOK": "Outlook", "jbs_REFERRAL_PORTAL": "Referral portal", "jbs_USER_REFERRAL": "User referral", "jbs_SCHOOL_SPRING": "School spring", "jbs_MONSTER": "Monster", "jbs_GMAIL": "Gmail", "jbs_CAREER_PORTAL": "Career Portal", "jbs_NEUVOO": "Neuvoo", "similar": "Similar", "legal_details": "Legal details", "expectations": "Expectations", "workplace_preference": "Workplace preference", "job_type_preference": "Job type preference", "expected_salary_range_rangename": "Expected salary range ({range})", "y_c_search_th_candid_id": "You can search the candidate via this ID", "profile_id": "Profile Id", "mark_up_percent": "Mark up %", "questions_preference": "Questions preference", "list_name": "List name", "mas_wrng_ans": "Max wrong answer", "wrong_asnwer": "Wrong answer", "minimum_wrng_answ": "Minimum / wrong answer", "wrng_answ_desc": "Candidates and applicants will be automatically rejected upon reaching the maximum number of wrong answers and will receive auto-reply notifications via email and in-app", "point": "Point", "view_type": "View type", "radio_button": "Radio button", "job_deleted_successfully": "Job deleted successfully", "delete_project": "Delete project", "delete_project_msg": "Are you sure you want to delete this project?", "project_deleted_successfully": "Project deleted successfully", "other_contacts": "Other contacts", "enter_valid_linkedin_url": "Enter a valid Linkedin URL", "enter_valid_facebook_url": "Enter a valid Facebook URL", "enter_valid_twitter_url": "Enter a valid X (Twitter) URL", "ssn_helper_text": "First 5 digits/Last 4 digits", "candidate_submitted": "Candidate submitted", "your_candidate_submitted_success": "Your candidate submitted successfully", "candidate_exists__add_more": "The candidate's info exists and is updated. However, You can add more details", "no_similar_candidate_found": "We couldn't find any similar candidate", "SkillAlreadyExistInProfileException": "Skill is duplicated", "LanguageAlreadyExistInProfileException": "Language is duplicated", "linked_by_recruiter": "Linked by recruiter", "linked_jobs": "Linked jobs", "linked_jobs_tooltip": "List of jobs that candidate linked to previously", "activity_candidate_selected": "<b>{name}</b> linked <b>{name}</b> to <b>{name}</b>", "activity_candidate_reselected": "<b>{name}</b> relinked <b>{name}</b> as a candidate", "activity_meeting_scheduled": "<b>{name}</b> scheduled a meeting with <b>{name}</b>", "manual_creation": "Manual creation", "no_candidate_found": "No Candidate found", "candidate_not_linked_to_any_job": "This candidate is not linked to any job", "candidate_not_applied_to_any_job": "This candidate is not applied to any job", "link_jobs": "Link jobs", "submit_to_vendor": "Submit to vendor", "share_candidate": "Share candidate", "delete_candidate": "Delete candidate", "are_you_sure_delete_candidate": "Are you sure you want to delete this candidate?", "no_tags": "No tags", "add_tag": "Add tag", "no_similar_found": "No similar found", "no_activities_for_candidate_desc": "There has been no activity for this candidate so far", "ques_with_num": "Question {num}", "no_result_f_f_y_s": "No results found for your search", "meeting_title_required": "Meeting title is required", "forward_to": "Forward to", "applicant": "Applicant", "latest_activity": "Latest activity", "condition_changed_successfully": "{condition} changed successfully", "login_to_apply": "Login to apply", "candidate_tags": "Candidate tags", "vendor": "<PERSON><PERSON><PERSON>", "max_file_size_to_upload": "You can upload maximum 100 MB", "clear_chat": "Clear chat", "exit_group": "Exit group", "lang_wit_level": "{lang} ({level})", "CERTIFICATE": "Certificate", "no_jobs_found": "No jobs found", "no_job_to_link": "There are no jobs to link", "s_b_g_t_m_s": "It should be more than the minimum salary", "s_b_l_t_m_s": "It should be less than the maximum salary", "enter_valid_username": "Enter valid username", "residency_n_legal_information": "Residency and Legal Information", "background_n_referrals": "Background and Referrals", "discover_t_p_p_f_name": "Discover the premium plans for {name}", "DuplicatedCandidateException": "This candidate already exists", "helper_tax_term": "<b>Tax term:</b> Refers to the period or terminology used in the context of taxation", "helper_markup": "<b>Markup:</b> Essentially consisting of Base Salary, Benefits Cost and Other Costs", "present": "Present", "delete_data": "Delete data", "are_you_sure_delete_candidate_data": "Are you sure you want to delete this candidate data?", "lobox_user_candidacy_delete_confirmation_alert_message": "Linked jobs will be terminated, and appended data will delete", "manualcreated_candidacy_delete_confirmation_alert_message": "Linked jobs will be terminated, and the candidate will be removed from your list", "b_t_c_is_l_u_t_w_s_a_i_y_s_n_l": "Because the candidate is Lobox user, they will still appear in your search and list", "share_project": "Share project", "must_be_less_n_char": "Must be less than {char} characters", "close_job": "Close job", "close_job_msg": "Are you sure you want to close the job?", "close_job_alert": "Closed jobs automatically remove after 1 month if not reopened", "archive_instead": "Archive instead", "switch_to_portal": "Switch to {name}", "candidate_link_job_desc": "This will start hiring process for candidate", "linked": "Linked", "suggested": "Suggested", "manually_created": "Manually created", "assignees": "Assignees", "assignee_updated": "Assignee updated", "assignee_updated_successfully": "Assignees updated successfully for this project", "link_candidate": "Link candidate", "legal_documents_for_regarding_residency_and_tax_details": "Legal documents for regarding residency and tax details", "background_disclosures_check_works_and_referrals": "Background disclosures check works and referrals", "id": "Id", "submit_to_client": "Submit to client", "search_candidates": "Search candidates", "no_candidate_to_link": "There are no candidates to link", "project_name_exist": "Project name already exists. Please choose a unique name", "within_1_day": "Within 1 day", "within_2_days": "Within 2 days", "within_3_days": "Within 3 days", "within_1_week": "Within 1 week", "within_2_weeks": "Within 2 weeks", "within_1_month": "Within 1 month", "within_1_year": "Within 1 year", "siz_months": "Six months", "num_hours_per_week": "{hour} hours per week", "entity_copy_text": "You can search the {entity} via this Id", "job_published": "Job published", "t_c_i_h_b_c_t_y_c": "The candidate id has been copied to your clipboard", "text": "Text only", "drop_down": "Drop down", "no_antity_in_object": "There are no {entity}, try to {action}", "create_one": "Create one", "write_one": "Write one", "write_review": "Write review", "checklists": "Checklists", "job_created": "Job created", "project_created": "Project created", "project_updated": "Project updated", "project_updated_successfully": "Project updated successfully", "edit_project": "Edit project", "delete_entity_alert": "In order to delete a {entity}, you must enter the {entity} name:", "entity_name": "{entity} name", "delete_job_msg": "Are you sure you want to delete this job?", "p_c_n_d_w_l_j": "The project can't be deleted with linked jobs", "l_j_w_b_t_a_c_w_b_r_f_y_l": "Linked jobs will be terminated, and the candidate will be removed from your list", "l_j_w_b_t_a_a_d_w_b_r_f_y_l": "Linked jobs will be terminated, and appended data will be deleted", "b_t_c_i_l_u_t_w_a_i_s_n_l": "Because the candidate is a Lobox user, they will still appear in your search and list", "copy_id": "Copy ID", "id_copied": "ID copied", "threads": "Threads", "test": "Test", "emails": "Emails", "DONE": "Done", "ON_HOLD": "On hold", "write_todo": "Write todo", "write_note": "Write note", "your_ratings": "Your ratings", "last_modified_by": "Last modified by", "add_document": "Add document", "visibility": "Visibility", "filters": "Filters", "paste_previous_one": "Paste previous one", "personal_info_and_resume": "Personal info & resume", "label_optional": "{question} <b>(optional)</b>", "total_questions": "Total questions", "questions_answered": "Questions answered", "job_app_success_title": "Congrats, Your application is completed!", "job_app_success_desc": "Application results will be emailed to you. You can also track your job status in real time on Lobox", "update_profile_desc": "Do you want to update your profile with the newly added resume?", "lack_of_data_job_application": "Your profile is incomplete, which may affect recruiters decisions. Please complete your profile for a stronger professional profile before applying for a job", "work_authorization_helper": "Acceptable candidate locations and permits for this job", "authorization_type": "Authorization type", "should_set_date_first": "You need to set date first", "should_set_time": "You need to set time", "update_profile_options": "Update profile options", "d_y_w_t_a_t_r_t_y_p_o_over": "Do you want to add this resume to your profile or overwrite the existing one?", "overwrite": "Overwrite", "y_n_r_w_r_t_t_c_p": "Your new resume will replace to the current profile", "append_t_existing": "Append to existing", "y_p_w_b_u_w_t_n_d_f_n_r": "Your profile will be updated with the new data from new resume", "job_tags": "Job tags", "candidates_linked_successfully": "Candidate(s) linked successfully", "link_candidate_description": "Linking candidates will start hiring process for them", "association": "Association", "edit_stages": "Edit stages", "person_commented_on_task": "{person} commented on {task} task", "person_updated_comment_on_task": "{person} {action} {pronoun} comment on {task} task", "view_comment": "View comment", "pipelines_and_automation": "Pipelines and automation", "automate_application_form": "Automate application form", "a_r_s_w_i_a_f_f_a": "Automating Review stage will impact application forms for applicants", "unpublish_job_alert": "Your progress is saved under your unpublished jobs, can continue or delete it from there", "id_doc_type_TRANSCRIPTS": "Transcripts", "id_doc_type_EML_FILE": "EML File", "id_doc_type_PASSPORT": "Passport", "id_doc_type_DRIVING_LICENCE": "Driving licence", "id_doc_type_RESUME": "Resume", "id_doc_type_SSN": "SSN", "id_doc_type_DIPLOMA": "Diploma", "residency_location": "Residency location", "residency_location_help_text": "This is candidate legal right of work location", "residency_location_required_alert_message": "You must select the candidate legal residency first to complete the forms", "clearance": "Clearance", "acc_file_types_x_max_x": "Accepted file types: {name}", "compare": "Compare", "expand": "Expand", "automation_panel": "Automation panel", "stage_emoty_description": "Automate stages to have smoother hiring process", "automate_stage": "Automate stage", "bulk_action": "Bulk action", "change_color": "Change color", "sort": "Sort", "delete_stage_desc2": "You need to move or reject all candidates to delete stage", "selected_items": "{count} of {count} selected", "move_to": "Move to", "move_all": "Move all", "reject_all": "Reject all", "template": "Template", "send_interview": "Send interview", "modify_template": "Modify template", "bulk_actions_require_templates": "Bulk actions requires to use templates", "delete_stage_alert": "You have to remove all active candidates under the stage before deletion", "rejections": "Rejections", "delete_default_stage_alert": "Default stages can not be deleted", "by_date": "By date", "by_name": "By name", "by_rating": "By rating", "blue": "Blue", "orange": "Orange", "red": "Red", "green": "Green", "purple": "Purple", "yesterday_cap": "Yesterday", "search_notes": "Search notes", "no_document_found": "No document found", "no_document_found_desc": "There are no document for the candidate", "no_meeting_found": "No meeting found", "no_meeting_found_desc": "There are no meetings scheduled for this candidate", "date_n_time": "Date and time", "member_availability": "Member availability", "specific_time": "Specific time", "choose_availability": "Choose availability", "no_notes_found": "No notes found", "no_notes_found_desc": "There are no notes for the candidate", "no_todos_found": "No todos found", "no_todos_found_desc": "There are no todos for the candidate", "no_review_found": "No review yet", "no_review_found_desc": "There are no reviews for the candidate", "not_set": "Not set", "deleted_account": "Deleted account", "view_company": "View company", "visible_to_your_team": "Visible to your team", "visible_to_you_only": "Visible to you only", "edit_review": "Edit review", "x_of_y": "<b>{index}</b> of <b>{total}</b>", "x_of_y_selected": "{index} of {total} selected", "ONE_WEEK": "1 Week", "TWO_WEEKS": "2 Weeks", "ONE_MONTH": "1 Month", "TWO_MONTHS": "2 Months", "THREE_MONTHS": "3 Months", "MORE_THAN_THERE_MONTHS": "More than 3 Months", "looking_for_more": "Looking for more...?", "choose_an_action": "Choose an action", "selected_jobs": "Selected jobs", "link_candidates": "Link candidates", "change_status": "Change status", "change_priority": "Change priority", "change_project_links": "Change project links", "bulk_job_action": "Bulk job action", "bulk_job_action_helper": "Set bulk actions for your jobs", "batch_status_alert_message": "Choose the status you want to assign to this job", "batch_priority_alert_message": "Choose the priority you want to assign to this job", "batch_action_alert": "These actions cannot be undone, be cautious!!!", "changing_status": "Changing status", "changing_priority": "Changing priority", "actions": "Actions", "changing_projects": "Changing projects", "status_updated": "Status updated", "priority_updated": "Priority updated", "project_links_updated": "Project links updated", "batch_status_success": "The status of selected jobs updated successfully", "batch_priority_success": "The priority of selected jobs updated successfully", "batch_projects_success": "The project links of selected jobs updated successfully", "invitation_accepted": "Meeting is added to your calendar", "invitation_declined": "Invitation declined successfully", "set_as_default": "Set as default", "remove_as_default": "Remove as default", "person_scheduled_meeting_through_availability": "{person} scheduled a meeting through your availability", "search_activities": "Search activities", "candidate_moved": "Candidate moved", "selected_candidates_moved": "Selected candidates moved", "note_added": "Note added", "note_added_to_selected_candidates": "Note added to selected candidates", "todo_added": "<PERSON><PERSON> added", "todo_added_to_selected_candidates": "<PERSON><PERSON> added to selected candidates", "viewed_on": "Viewed on", "candidate_linked_to_your_jobs": "Candidate is linked to {count} of your jobs previously", "compare_candidates": "Compare candidates", "add_candidate": "Add candidate", "candidates_list": "Candidates list", "name_removed_name": "<b>{name}</b> removed <b>{name}</b>", "name_added_name": "<b>{name}</b> added <b>{name}</b>", "track_application": "Track application", "assessments": "Assessments", "application_date": "Application date", "current_stage": "Current stage", "job_tracking_description": "You’ll receive email and in-app notifications when recruiters update your application status", "previous_stages": "Previous stages", "user_hired_text": "Congrats, You’ve successfully completed every stage of hiring process", "tests_coming": "Tests is coming up", "num_questions_answered": "{answered} Questions / {total} Answered", "on_stage": "On {stage} stage", "no_meetings_found": "No meetings found", "no_meetings_found_desc": "There are no meetings scheduled for this candidate", "add_to_compare": "Add to compare", "remove_assignee": "Remove assignee", "poc_cant_remove": "Point of contact can't be removed", "meeting_preferences": "Meeting preferences", "integration_subtitle": "Integrations with different platforms", "r_y_s_y_w_t_t_ow_name": "Are you sure you want to transfer ownership to <b>{name}</b>?", "transfer": "Transfer", "y_w_n_l_h_ac_t_t_g_o_new_msg": "You will no longer have access to the group or its new messages", "tap_t_u_r": "Tap to upload resume", "InvalidFileTypeException": "Wrong file type", "unable_to_s": "Unable to message", "GROUP_CREATED_ACT": "<b>{name}</b> group created", "GROUP_DELETED_ACT": "<b>{name}</b> group deleted", "GROUP_MEMBER_ADDED_ACT": "<b>{name}</b> added <b>{name}</b>", "GROUP_MEMBER_LEFT_ACT": "<b>{name}</b> left group", "GROUP_MEMBER_KICKED_ACT": "<b>{name}</b> removed <b>{name}</b>", "GROUP_GRANTED_ADMIN_ACT": "<b>{name}</b> set <b>{name}</b> as Admin", "GROUP_REVOKED_ADMIN_ACT": "<b>{name}</b> revoked <b>{name}</b> as Admin", "GROUP_OWNERSHIP_TRANSFERRED_ACT": "<b>{name}</b> set <b>{name}</b> as Owner", "youre_not_admin_or_owner": "You are not the owner or admin of this page", "for_premium_contact_admin": "To obtain a premium package, request it from the owner or an admin", "no_notif_w_b_s_t_blo_b_if_y": "No notifications will be sent about the block, but if you are following the account, you will be removed from their list", "when_a_ac_i_s_p_y_v_th": "When an account is set to public, you can view their content, but interaction is not permitted", "un_t_any_via_set_o_p_b": "Unblock them anytime via settings or their profile, but you'll need to follow them again to interact", "block_and_report": "Block and report", "what_happens": "What happens:", "fit": "Fit", "manage_visibility": "Manage visibility", "manage_profile_visibility": "Manage your profile’s visibility and the public data shown to other users", "use_resume_to_fill_profile": "Use your uploaded resume to automatically fill and update your profile", "w_c_f_an_acc_t_email_p_c": "We couldn’t find an account with this email. Please check the email you entered, or sign up if you haven’t created an account yet", "account_not_found": "Account not found", "FILTER_PAST_3_MONTHS": "Past 3 months", "responded": "Responded", "priority_low": "P4-Low", "priority_medium": "P3-Medium", "priority_high": "P2-High", "priority_highest": "P1-Highest", "ticket_id": "Ticket ID", "editTicket": "Edit ticket", "createTicket": "Create ticket", "ticket_priority_infobox": "Lobox Support prioritizes tickets based on their urgency", "person_assigned_taskTitle_to_person2": "{person} assigned {taskTitle} to {person2}", "person_accepted_taskTitle_task": "{person} accepted {taskTitle} task", "person_declined_taskTitle_task": "{person} declined {taskTitle} task", "task_invitation_accepted": "Task is added to your calendar", "task_invitation_declined": "Task declined successfully", "you_are_not_member": "You are not member", "some_f_ex_all_siz_number": "Some files exceed the allowed size of {size}MB", "max_file_size_ex": "Maximum file size exceed", "msg_shared": "Message shared", "search_by_name_or_id": "Search by name or username", "write_a_comment": "Write a comment", "commenting_is_locked": "Commenting is locked", "there_is_no_comment": "There is no comment", "no_comment_desc": "Write a comment and start your conversation", "compare_all_features": "Compare all features", "all_features": "All Fearures", "select_plan": "Select plan", "unfollow_name": "Unfollow {name}", "unfollow_name_bold": "Unfollow <b>{name}</b>?", "following_name": "Following {name}", "stop_following_name": "Stop following {name}", "result": "Result", "enter_your_payment_details": "Enter your payment details", "card_number": "Card number", "name_on_payment_method": "Name on payment method", "security_code": "Security code", "complete_checkout": "Complete checkout", "billing_address": "Billing address", "city_town_village": "City/town/village", "province_region": "Province/region", "vat_ID": "VAT/GST ID", "resume_parsing_notification": "Resume parsing", "profile_visibility_notification": "Profile visibility", "task_invitation_notification": "Task invitation notification", "no_description": "No description", "r_y_s_y_w_del_post": "Are you sure you want to delete this post?", "usr_hdr_parent": "Digital Experience with Unique Ecosystem", "usr_hdr_chld_1": "Empower connections, amplify voices, and redefine interactions for vibrancy", "usr_hdr_chld_2": "Unleash your content's potential to resonate and ignite meaningful connections", "usr_follow_parent": "Join, Follow, Connect", "usr_follow_chld_1": "Follow people and pages", "usr_follow_chld_2": "Invite followers to join your journey", "usr_follow_chld_3": "Stand out when you feel lost in the crowd", "usr_post_parent": "In<PERSON><PERSON>, Create, Captivate", "usr_post_chld_1": "Amplify your voice with insightful posts", "usr_post_chld_2": "People need your insights and inspirations", "usr_post_chld_3": "Unleash your creativity to illuminate minds", "usr_cmmnt_parent": "Interact, Influence, Shape", "usr_cmmnt_chld_1": "Every comment and reaction sparks change", "usr_cmmnt_chld_2": "Engage with posts to be part of the story", "usr_cmmnt_chld_3": "Shape the narrative because your voice matters", "usr_clubs_parent": "Unite, Expand, Flourish", "usr_clubs_chld_1": "Create your own club with like-minded people", "usr_clubs_chld_2": "Connect, empower, and engage in communities", "usr_clubs_chld_3": "Explore communities that go beyond the ordinary", "usr_page_parent": "Build, Lead, Earn", "usr_page_chld_1": "Craft your page by empowering your platform", "usr_page_chld_2": "Venture from vision to reality to shape future", "usr_page_chld_3": "Elevate your mission to new heights", "usr_news_parent": "Keep updated, Stay tuned, Move ahead", "usr_news_chld_1": "Stay connected with news", "usr_news_chld_2": "Dive deep into the stories beyond headlines", "usr_news_chld_3": "Streamline your world by staying ahead", "usr_artcls_parent": "Read, Reflect, Transform", "usr_artcls_chld_1": "Stay enlightened with enlightening articles", "usr_artcls_chld_2": "Dive deeper beyond the surface", "usr_artcls_chld_3": "Refine your perspective with pivotal insights", "usr_jobs_parent": "Discover, Find, Thrive", "usr_jobs_chld_1": "Track your job applications at every phase", "usr_jobs_chld_2": "Interact closely with companies differently", "usr_jobs_chld_3": "Navigate your career path with opportunities", "usr_srvc_parent": "Discover, Learn, Thrive", "usr_srvc_chld_1": "Find experts ready to assist you 24/7", "usr_srvc_chld_2": "Enhance your experience with quality services", "usr_srvc_chld_3": "Thrive with every service you receive", "usr_cmpgn_parent": "<PERSON>ver, <PERSON><PERSON><PERSON>, <PERSON><PERSON>", "usr_cmpgn_chld_1": "Discover campaigns that fit your budget", "usr_cmpgn_chld_2": "Engage with content that speaks to you", "usr_cmpgn_chld_3": "Find inspiration in campaigns that move you", "usr_sales_parent": "Explore, Purchase, Enjoy", "usr_sales_chld_1": "Engage with sales offers that fit your needs", "usr_sales_chld_2": "Connect with sales content tailored to you", "usr_sales_chld_3": "Find inspiration in sales initiatives for joy", "usr_schdl_parent": "Schedule, Sync, Succeed", "usr_schdl_chld_1": "Master your time with seamless scheduling", "usr_schdl_chld_2": "Organize, participate video-conferences", "usr_schdl_chld_3": "Schedule your precious time with precision", "usr_avlblty_parent": "Share, Schedule, Meet", "usr_avlblty_chld_1": "Offer your availability with ease", "usr_avlblty_chld_2": "Allow others to select meeting times easily", "usr_avlblty_chld_3": "Sync meetings for smooth scheduling", "edtr_hdr_parent": "Comprehensive Digital Management", "edtr_hdr_chld_1": "Manage your pages, posts, news, articles, and club engagements seamlessly", "edtr_hdr_chld_2": "Revolutionize digital interaction with an integrated, user-friendly approach", "edtr_page_parent": "Optimize, Update, Excel", "edtr_page_chld_1": "Enhance visual appeal and user experience", "edtr_page_chld_2": "Regularly update information and features", "edtr_page_chld_3": "Optimize layout and content organization", "edtr_post_parent": "Design, Deliver, Captivate", "edtr_post_chld_1": "Craft compelling, engaging posts", "edtr_post_chld_2": "Use storytelling and visuals to captivate", "edtr_post_chld_3": "Continuously innovate content strategy", "edtr_postintrctn_parent": "Connect, Converse, Cultivate", "edtr_postintrctn_chld_1": "Actively engage with comments and feedback", "edtr_postintrctn_chld_2": "Encourage community discussions", "edtr_postintrctn_chld_3": "Refine approach using engagement data", "edtr_news_parent": "Create, Inform, Lead", "edtr_news_chld_1": "Craft news that resonates with global audiences", "edtr_news_chld_2": "Deliver stories that are impactful and influential", "edtr_news_chld_3": "Lead with insights essential to your audience", "edtr_artcls_parent": "Write, Inspire, Influence", "edtr_artcls_chld_1": "Create enlightening, engaging content", "edtr_artcls_chld_2": "Shape understanding and inspire thought", "edtr_artcls_chld_3": "Offer pivotal insights to transform perspectives", "edtr_clubs_parent": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "edtr_clubs_chld_1": "Create spaces for like-minded individuals", "edtr_clubs_chld_2": "Empower and invigorate communities", "edtr_clubs_chld_3": "Cultivate communities for shared success", "edtr_schdl_parent": "Plan, Coordinate, Achieve", "edtr_schdl_chld_1": "Enable seamless time management", "edtr_schdl_chld_2": "Lead impactful video conferences", "edtr_schdl_chld_3": "Streamline meetings and events", "edtr_pipeln_parent": "Collaborate, Craft, Circulate", "edtr_pipeln_chld_1": "Streamline collaboration and content creation", "edtr_pipeln_chld_2": "Enhance content with diverse perspectives", "edtr_pipeln_chld_3": "Efficiently manage and distribute content", "edtr_automtn_parent": "Categorize, Track, Automate", "edtr_automtn_chld_1": "Implement dynamic labeling and automation", "edtr_automtn_chld_2": "Maintain oversight with advanced tracking tools", "edtr_automtn_chld_3": "Automate content creation and distribution", "edtr_dashbrd_parent": "Monitor, Me<PERSON>ure, Magnify", "edtr_dashbrd_chld_1": "Access engagement metrics at a glance", "edtr_dashbrd_chld_2": "Analyze audience behavior and interactions", "edtr_dashbrd_chld_3": "Optimize content strategies with real-time data", "recrtr_hdr_parent": "Recruitment Revolution", "recrtr_hdr_chld_1": "Intuitive portal for top-match candidates and comprehensive hiring tools", "recrtr_hdr_chld_2": "Streamline digital talent acquisition to boost productivity and efficiency", "recrtr_canddt_parent": "AI-Driven Discovery", "recrtr_canddt_chld_1": "Use AI to find best-matching candidates", "recrtr_canddt_chld_2": "Ensure perfect fit between talent and team", "recrtr_canddt_chld_3": "Elevate hiring with intelligent matchmaking", "recrtr_jobs_parent": "Strategic Hiring", "recrtr_jobs_chld_1": "Connect with top candidates for team success", "recrtr_jobs_chld_2": "Monitor applications with precision", "recrtr_jobs_chld_3": "Engage potential hires with unique interactions", "recrtr_schdl_parent": "Interview Coordination", "recrtr_schdl_chld_1": "Streamline interview scheduling in recruitment", "recrtr_schdl_chld_2": "Manage and conduct interviews efficiently", "recrtr_schdl_chld_3": "Coordinate interviews seamlessly with the team", "recrtr_avlblty_parent": "Time Synchronization", "recrtr_avlblty_chld_1": "Share availability with a user-friendly interface", "recrtr_avlblty_chld_2": "Allow candidates to select meeting times easily", "recrtr_avlblty_chld_3": "Sync meetings with calendars smoothly", "recrtr_ats_parent": "Talent Tracker", "recrtr_ats_chld_1": "Simplify candidate management with efficiency", "recrtr_ats_chld_2": "Assess and sort applicants precisely", "recrtr_ats_chld_3": "Enhance recruitment with integrated tools", "recrtr_psthrngppln_parent": "Seamless Onboarding", "recrtr_psthrngppln_chld_1": "Track documentation from hiring to onboarding", "recrtr_psthrngppln_chld_2": "Conduct background checks and screenings", "recrtr_psthrngppln_chld_3": "Transition new hires confidently into their roles", "recrtr_automtn_parent": "Efficiency Engine", "recrtr_automtn_chld_1": "Use dynamic labels and to-dos for organization", "recrtr_automtn_chld_2": "Track tasks and milestones efficiently", "recrtr_automtn_chld_3": "Automate hiring to maximize productivity", "recrtr_dashbrd_parent": "Insights Interface", "recrtr_dashbrd_chld_1": "Graphical access to recruitment metrics.", "recrtr_dashbrd_chld_2": "Analyze hiring efforts and pipeline dynamics", "recrtr_dashbrd_chld_3": "Refine strategies with real-time data efficiently", "srvc_hdr_parent": "Service Transformation", "srvc_hdr_chld_1": "Platform for professionals to showcase services and manage clients efficiently", "srvc_hdr_chld_2": "Streamlines client interaction and service, setting a new digital standard", "srvc_peplpags_parent": "Provide, Partner, Thrive", "srvc_peplpags_chld_1": "Use AI to showcase services, enhancing visibility", "srvc_peplpags_chld_2": "Form partnerships aligned with your expertise", "srvc_peplpags_chld_3": "Elevate practice on an advanced platform", "srvc_srvcs_parent": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>", "srvc_srvcs_chld_1": "Showcase expertise and serve clients nonstop", "srvc_srvcs_chld_2": "Elevate your professional journey", "srvc_srvcs_chld_3": "Build success with every service offered", "srvc_schdl_parent": "Book, Serve, Synchronize", "srvc_schdl_chld_1": "Simplify scheduling for client satisfaction", "srvc_schdl_chld_2": "Manage services effortlessly", "srvc_schdl_chld_3": "Align appointments with your availability", "srvc_avlblty_parent": "Display, Arrange, Connect", "srvc_avlblty_chld_1": "Share availability with an intuitive interface", "srvc_avlblty_chld_2": "Enable clients to book within open slots", "srvc_avlblty_chld_3": "Synchronize appointments across calendars", "srvc_cts_parent": "Service Navigator", "srvc_cts_chld_1": "Streamline client management for efficiency", "srvc_cts_chld_2": "Organize client interactions to enhance service", "srvc_cts_chld_3": "Improve outcomes with integrated tools", "srvc_automtn_parent": "Workflow Wizard", "srvc_automtn_chld_1": "Implement dynamic labeling and task lists", "srvc_automtn_chld_2": "Maintain oversight with enhanced tracking", "srvc_automtn_chld_3": "Automate processes to boost efficiency", "srvc_dashbrd_parent": "Performance Panorama", "srvc_dashbrd_chld_1": "View service metrics through a graphical interface", "srvc_dashbrd_chld_2": "Analyze feedback and metrics for improvements", "srvc_dashbrd_chld_3": "Refine service approach with actionable insights", "cmpgn_hdr_parent": "Marketing Revolution", "cmpgn_hdr_chld_1": "Uses state-of-the-art marketing tools to revolutionize campaign strategies", "cmpgn_hdr_chld_2": "Enhances campaign management with a next-generation marketing tools", "cmpgn_peplpags_parent": "Reach, <PERSON>tisfy, Su<PERSON>eed", "cmpgn_peplpags_chld_1": "Leverage AI to reach ideal customers", "cmpgn_peplpags_chld_2": "Align campaigns with audience needs", "cmpgn_peplpags_chld_3": "Elevate brand with impactful connections", "cmpgn_cmpgns_parent": "Create, Connect, Conquer", "cmpgn_cmpgns_chld_1": "Craft compelling narratives", "cmpgn_cmpgns_chld_2": "Connect personally with audiences", "cmpgn_cmpgns_chld_3": "Achieve goals with resonant campaigns", "cmpgn_ads_parent": "Create, Captivate, Recognize", "cmpgn_ads_chld_1": "Design ads that captivate and retain interest", "cmpgn_ads_chld_2": "Boost recognition and accelerate market growth", "cmpgn_ads_chld_3": "Achieve success with positive outcomes", "cmpgn_schdl_parent": "Schedule, Engage, Harmonize", "cmpgn_schdl_chld_1": "Facilitate easy scheduling with customers", "cmpgn_schdl_chld_2": "Manage and coordinate campaign discussions", "cmpgn_schdl_chld_3": "Align meetings with team & customer", "cmpgn_avlblty_parent": "Share, Book, Unite", "cmpgn_avlblty_chld_1": "Present availability with an intuitive interface", "cmpgn_avlblty_chld_2": "Allow customers to book campaign meetings", "cmpgn_avlblty_chld_3": "Sync meetings across calendars seamlessly", "cmpgn_cts_parent": "Drive Collaboration, Measure Success", "cmpgn_cts_chld_1": "Drive collaboration and measure success", "cmpgn_cts_chld_2": "Foster collaboration for productivity", "cmpgn_cts_chld_3": "Achieve thorough outcomes with collaboration", "cmpgn_automtn_parent": "Campaign Catalyst", "cmpgn_automtn_chld_1": "Automate categorization, dynamic workflows", "cmpgn_automtn_chld_2": "Track campaign progress and metrics", "cmpgn_automtn_chld_3": "Refine targeting and delivery with automation", "cmpgn_dashbrd_parent": "Campaign and Advertisement Insights", "cmpgn_dashbrd_chld_1": "Access in-depth metrics via graphical dashboard", "cmpgn_dashbrd_chld_2": "Analyze reach, engagement, and conversion", "cmpgn_dashbrd_chld_3": "Optimize campaigns with real-time feedback", "sales_hdr_parent": "Sales Transformation", "sales_hdr_chld_1": "Sell your products effortlessly and reach more buyers with our marketplace", "sales_hdr_chld_2": "Transforms sales with advanced tools for streamlined automation and strategies", "sales_peplpags_parent": "Identify, Interact, Close", "sales_peplpags_chld_1": "Use AI to identify and reach potential customers", "sales_peplpags_chld_2": "Align sales strategies with audience preferences", "sales_peplpags_chld_3": "Drive growth with impactful engagement", "sales_sales_parent": "Sales Strategy", "sales_sales_chld_1": "Develop persuasive sales strategies", "sales_sales_chld_2": "Personal lead engagement, convert to customers", "sales_sales_chld_3": "Achieve sales targets with inspiring strategies", "sales_prmtn_parent": "Showcase, Captivate, Sell", "sales_prmtn_chld_1": "Create compelling product presentations", "sales_prmtn_chld_2": "Boost sales via strategic promotions", "sales_prmtn_chld_3": "Drive engagement and results with each effort", "sales_schdl_parent": "Sales Meetings", "sales_schdl_chld_1": "Facilitate seamless scheduling with a calendar", "sales_schdl_chld_2": "Coordinate sales discussions effortlessly", "sales_schdl_chld_3": "Sync meetings with team & customer", "sales_avlblty_parent": "Scheduler Efficiency", "sales_avlblty_chld_1": "Showcase availability with an intuitive interface", "sales_avlblty_chld_2": "Let customers book meetings in available slots", "sales_avlblty_chld_3": "Sync meetings across calendars smoothly", "sales_cts_parent": "Customer Insights", "sales_cts_chld_1": "Monitor to lead journeys through staged pipelines", "sales_cts_chld_2": "Personalize interactions from contact to closing", "sales_cts_chld_3": "Enhance performance with data-driven tools", "sales_automtn_parent": "Sales Efficiency", "sales_automtn_chld_1": "Dynamic categorization, automated workflows", "sales_automtn_chld_2": "Track sales progress and performance metrics", "sales_automtn_chld_3": "Automate targeting and processes for efficiency", "sales_dashbrd_parent": "Sales Insights", "sales_dashbrd_chld_1": "Access metrics via a graphical dashboard", "sales_dashbrd_chld_2": "Analyze reach, engagement, conversion metrics", "sales_dashbrd_chld_3": "Optimize strategies with real-time feedback", "plan_subscription": "Plan subscription", "recurring_payment": "Recurring payment", "cancel_subscription": "Cancel subscription", "new_member": "New member", "plan_is_activated": "{congratulations}! Your {planName} plan is now active on your page.", "plan_is_downgraded": "Your {planName} has ended and your page has been downgraded to the free plan. You can explore our plans to find the one meets your needs.", "plan_is_renewed": "{congratulations}! Your {planName} plan has been successfully renewed for your page.", "plan_subscription_canceled": "Your subscription has been canceled. Your page will be downgraded to the Free plan, and access to {Recruiter} {Premium} features will be removed. You’ll receive a refund based on your remaining days and active seats.", "congratulations": "Congratulations", "business_profiles": "Business Profiles", "IN_PROGRESS": "In progress", "calendar_name": "Calendar name", "icloud_calendar_integration": "iCloud Calendar integration", "iCloud_integ_helper_title": "How to integrate iCloud calendar?", "iCloud_integ_helper_item_1": "1. Go to iCloud Calendar on a desktop.", "iCloud_integ_helper_item_2": "2. On the left sidebar, find the calendar you want to share.", "iCloud_integ_helper_item_3": "3. Click the (user icon) button next to it.", "iCloud_integ_helper_item_4": "4. Check \"Public Calendar\".", "iCloud_integ_helper_item_5": "5. <PERSON><PERSON> the generated URL.", "iCloud_integ_helper_item_6": "6. Paste this link into the input above.", "iCloud_added": "iCloud Calendar has been successfully integrated.", "URL_incorrect": "URL is not correct.", "email_incorrect": "<PERSON><PERSON> is not correct.", "cancel_plan_message": "Are you sure you want to cancel your subscription?", "cancel_plan_hint": "This action will cancel your auto-renewal for next month.", "PROJECT_CREATION": "Create Projects", "JOB_CREATION": "Create Jobs", "CANDIDATE_CUSTOM_FIELD": "Create Candidate Custom Fields", "INVITE_CANDIDATE": "In<PERSON><PERSON>", "CREATE_CANDIDATE": "Create Candidates", "ADD_CANDIDATE": "Add Candidates (Job-Level)", "TRACK_APPLICANT": "Track Applicants (Job-Level)", "AUTOMATED_RESPONSE_MOVEMENT": "Automated Responses (Movement)", "AUTOMATED_RESPONSE_MEETING": "Automated Responses (Meeting)", "AUTOMATED_RESPONSE_REJECTION": "Automated Responses (Rejection)", "AUTOMATED_RESPONSE_NOTE": "Automated Responses (Note)", "AUTOMATED_RESPONSE_MESSAGE": "Automated Responses (Message)", "AUTOMATED_RESPONSE_TODO": "Automated Responses (To-Do)", "AUTOMATED_RESPONSE_REPLY": "Automated Responses (Reply)", "PROVIDE_REVIEW_FOR_CANDIDATE": "Provide Reviews for Candidates", "PROJECT_SEARCH": "Search Projects", "JOB_SEARCH": "Search Jobs", "GIVE_PAGE_ACCESS": "", "ACCEPT_PAGE_ACCESS": "", "DECLINE_PAGE_ACCESS": "", "REVOKE_PAGE_ACCESS": "", "ADD_EXTERNAL_CALENDAR": "Calendar Integrations", "REMOVE_EXTERNAL_CALENDAR": "", "CONNECT_TO_EXTERNAL_CALENDAR": "", "DISCONNECT_TO_EXTERNAL_CALENDAR": "", "AVAILABILITY": "Availability", "MEETING_YOURS": "Create Meetings", "TODO_YOURS": "Create To-Dos (Tasks with <PERSON><PERSON><PERSON>)", "NOTE_YOURS": "Notes (Yours)", "NOTE_CANDIDATE": "Notes (Candidate-Level)", "AI_JOB_CREATION": "AI Tools (Job Creation)", "TODO_CANDIDATE": "To-<PERSON><PERSON> (Candidate-Level)", "MEETING_CANDIDATE": "Meetings (Candidate-Level)", "JOB_PROJECT": "Jobs (Project-Level)", "APPLICANT_PROJECT": "Applicants (Project-Level)", "APPLICANT_JOB": "Applicants (Job-Level)", "CANDIDATE_PROJECT": "Candidates (Project-Level)", "CANDIDATE_JOB": "Candidates (Job-Level)", "REVIEW_CANDIDATE": "Reviews (Candidate-Level)", "REVIEW_JOB": "Reviews (Job-Level)", "ACTIVITY_PROJECT": "Activities (Project-Level)", "ACTIVITY_CANDIDATE": "Activities (Candidate-Level)", "ACTIVITY_JOB": "Activities (Job-Level)", "COLLABORATION_PROJECT": "Collaborations (Project-Level)", "COLLABORATION_JOB": "Collaborations (Job-Level)", "SUPPORT": "Support", "MEETING_TEMPLATE": "Meeting Templates", "DOCUMENT_MANAGEMENT": "Document Management", "CANDIDATE_SEARCH_LIMITATION": "Candidate Match Accuracy (Quality)", "THREADS_JOB_LEVEL_MESSAGING": "Threads (Job-Level Messaging)", "THREADS_CANDIDATE_LEVEL": "Threads (Candidate-Level)", "THREADS_YOURS": "Threads (Yours)", "THREADS_TEAM_MEMBER_LEVEL": "<PERSON><PERSON><PERSON><PERSON> (Team Member-Level)", "THREADS_COMPANY_LEVEL": "Threads (Company-Level)", "seat_added": "seat added", "n_seat_refunded": "{n} Seat Refunded", "invoice_ID": "Invoice ID", "subtotal": "Subtotal", "payment_date": "Payment date", "remained_time": "Remained time", "total_cap": "Total", "billing_details": "Billing details", "view_billing_details": "View billing details", "add_seat_subtotal_hint": "When adding seats after purchasing a plan, the price and duration will be calculated based on the remaining time and days passed.", "refund_seat_total_hint": "When refunding seats after purchasing a plan, the refund amount will be calculated based on the remaining time and days passed.", "seats_cap": "Seats", "seats_label_hint": "The owner is counted as one seat", "tax_amount": "Tax amount", "tax_amount_label_hint": "For countries that do not have a VAT, GST, or any other international tax system, a standard 10% tax rate will be applied.", "security_code_validation_error": "Security code should be 3 digits or more", "expiration_date_format_error": "Inalid expiration Date format", "n_more_features": "{n} more features", "n_seats_added": "{n} seats added", "remaining_time_formatted": "{remaining} days - {Date}", "rate_limit_exceeded": "You're on the {PlanName} plan, which offers {limit} access to {FeatureName}. Please contact your administrator (Owner, <PERSON><PERSON> or Head) to upgrade the business plan and unlock full access to all Lobox {Portal} features.", "upgrade_plan": "Upgrade plan", "text_templates": "Text templates", "no_templates_found": "No templates found", "your_name": "Your name", "your_position": "Your position", "candidate_name": "Candidate name", "company_name": "Company name", "current_year": "Current year", "follow_up_after": "Follow up after", "has_followup_message": "Have follow-up message", "followup_checkbox_hint": "Send a follow-up if user did not take any action.", "dynamic_template_helper_text": "Use [ ] for dynamic data", "text_template_subtitle": "It helps to generate emails and descriptions quickly.", "questions_template_subtitle": "It helps to generate questions quickly.", "tests": "Tests", "tests_template_subtitle": "It helps to generate tests quickly.", "text_template_email_subtitle": "Setup auto responses trough email to candidates.", "text_template_messages_subtitle": "Setup messaging candidate in Lobox messenger.", "rejection": "Rejection", "text_template_rejection_subtitle": "Write rejection letters from templates.", "text_template_meetings_subtitle": "Schedule meeting descriptions for candidates.", "n_days": "{n} days", "1_week": "1 week", "n_weeks": "{n} weeks", "1_month": "1 month", "n_months": "{n} months", "candidate_score_vs_job": "Candidate score vs job", "APPLICANT": "Applicant", "CANDIDATE": "Candidate", "d_y_w_add_user_to_candidate": "Do you want to Add {user} as a candidate?", "edit_and_add": "Edit and Add", "candidate_added": "Candidate added", "candidate_added_message": "Your candidate successfully added to jobs", "automate": "Automate", "no_threads_yet": "No threads yet", "y_h_n_threads_w_the_candidate_y": "You have no threads with the candidate yet", "no_emails_found": "No emails found", "no_emails_found_desc": "There are no emails for this candidate", "no_candidates_found": "No Candidates found", "no_applicants_found": "No Applicants found", "create_job_payment_alert": "Each {job} listing in Lobox {Recruiter} requires a {$99} payment", "complete_payment_to_proceed": "Complete payment to publish your job.", "checkout_publish": "Checkout & publish", "payment_successful": "Payment successful", "payment_not_successful": "Payment not successful", "payment_error_message": "Incorrect information, Please try again", "thanks_for_purchasing_plan": "Thank you for purchasing our plan.", "thanks_for_your_purchase": "Thank you for your purchase.", "n_of_m_selected": "{n} of {m} selected", "rate_limit_exceeded_admin": "You're on the {PlanName} plan, which offers {limit} to {FeatureName}. Upgrade now to unlock full access to all Lobox {Portal} features.", "compare_and_upgrade": "Compare and upgrade", "limited_access": "limited access", "no_access": "no access", "ACTIVITY_HEAD_GET": "Activities (Company-Level)", "ACTIVITY_TEAM_MEMBER_GET": "Activities (Team Member-Level)", "ACTIVITY_YOURS_GET": "Activities (Yours)", "REVIEW_HEAD_GET": "Reviews (Company-Level)", "REVIEW_TEAM_MEMBER_GET": "Reviews (Team Member-Level)", "REVIEW_YOURS_GET": "Reviews (Yours)", "CANDIDATE_HEAD_GET": "Candidates (Company-Level)", "CANDIDATE_TEAM_MEMBER_GET": "Candidates (Team Member-Level)", "CANDIDATE_YOURS_GET": "Candidates (Yours)", "APPLICANT_HEAD_GET": "Applicants (Company-Level)", "APPLICANT_TEAM_MEMBER_GET": "Applicants (Team Member-Level)", "APPLICANT_YOURS_GET": "Applicants (Yours)", "JOB_HEAD_GET": "Jobs (Company-Level)", "JOB_TEAM_MEMBER_GET": "Jobs (Team Member-Level)", "JOB_YOURS_GET": "Jobs (Yours)", "PROJECT_HEAD_GET": "Projects (Company-Level)", "PROJECT_TEAM_MEMBER_GET": "Projects (Team Member-Level)", "PROJECT_YOURS_GET": "Projects (Yours)", "MEETING_HEAD_GET": "Meetings (Company-Level)", "MEETING_TEAM_MEMBER_GET": "Meetings (Team Member-Level)", "MEETING_YOURS_GET": "Meetings (Yours)", "MEETING_CANDIDATE_GET": "Meetings (Candidate-Level)", "NOTE_HEAD_GET": "Notes (Company-Level)", "NOTE_TEAM_MEMBER_GET": "Notes (Team Member-Level)", "NOTE_YOURS_GET": "Notes (Yours)", "NOTE_CANDIDATE_GET": "Notes (Candidate-Level)", "TODO_HEAD_GET": "To-Dos (Company-Level)", "TODO_TEAM_MEMBER_GET": "<PERSON><PERSON><PERSON><PERSON> (Team Member-Level)", "TODO_YOURS_GET": "To<PERSON><PERSON><PERSON> (Yours)", "TODO_CANDIDATE_GET": "To-<PERSON><PERSON> (Candidate-Level)", "SEE_SIMILAR_CANDIDATE": "See Similar Candidates", "COMPARE_CANDIDATE": "Compare Candidates (Job-Level)", "SHARE_JOBS_WITH_VENDORS": "Share Jobs with Vendors", "SUBMIT_CANDIDATES": "Submit Candidates", "SEARCH_COMPANIES": "Search Companies", "SAVE_SEARCH_RESULTS": "Save Search Results", "upcoming_meetings": "Upcoming meetings", "upcoming_meetings_notice": "You have {0} upcoming meeting with this candidate", "automation": "Automation", "select_stage": "Select Stage", "hiring": "Hiring", "c_t_t_r_t_y_c_f": "Create message templates to respond to your candidates faster.", "auto_interview": "Auto Interview", "s_i_a_b_o_t_s_w_c": "Set interviews automatically based on this stage with candidates.", "auto_assessment": "Auto Assessment", "s_n_t_a_t_q_o_t_a": "Send None-technical and technical questions or tests automatically ", "auto_move": "Auto Move", "m_c_t_o_o_b_s_q_s_s_a": "Move candidates to other stages based on forms, questions, stage, and more.", "auto_reject": "Auto Reject", "r_c_b_o_f_q_s_s_a": "Reject candidates based on forms, questions, stage, and more.", "auto_note": "Auto Note", "w_n_o_c_a": "Write notes on candidate automatically.", "auto_todo": "Auto Todo", "w_t_o_c_a_f_t_y": "Write todo on candidate automatically for your team.", "s_c_b_o_t_c_n_a": "Select candidates based on the\n certain needs automatically.", "m_a_b_o_c_d": "Move candidates automatically based on\n the details.", "m_c_t_o_o_b_s_q_s_a_s": "Move candidates based on the assessments", "m_c_t_o_o_b_s_q_s_a": "Move candidates based on the application", "if": "if", "success": "success", "error_occurred": "error_occurred", "auto_movement_updated": "Auto movement updated", "age": "Age", "is": "is", "is_not": "is not", "action": "Action", "min_age": "Min age", "max_age": "Max age", "enter_cover_letter_text": "Enter cover letter text", "enter_phone_number": "Enter phone number", "activated": "Activated", "auto_movement_removed": "Auto movement removed", "candidate_mode": "Candidate mode", "search_projects_or_id": "Search projects or Id", "search_candidate_name_j_t_l_s": "Search candidate name, job title, location, skill, or Id", "search_job_title_c_l_id": "Search job title, creator, location, or Id", "Interacted": "Interacted", "all_candidates_within_t_s": "All candidates within the system", "candidates_y_h_inter_w": "Candidates you have interacted with", "y_s_searches": "Your saved searches", "no_search_f_tdf_k_o_cys": "No results found, try different keywords or check your spelling.", "companies": "Companies", "searched_txt": "Searched text", "this_is_tsym_b_tsf": "This is the search you make by this saved filter", "save_filters": "Save filters", "give_y_f_a_m_n_tr": "Give your filter a meaningful name that resonates.", "filter_name": "Filter name", "filter_saved": "Filters saved", "yt_filters_s_sucss": "Your filters saved successfully.", "update_filters": "Update filters", "yt_filters_u_sucss": "Your filters updated successfully.", "yt_filters_d_sucss": "Your filters deleted successfully.", "filter_updated": "Filters updated", "filter_deleted": "Filter deleted", "re_order": "Reorder", "linked_projects": "Linked projects", "job_type_pref": "Job type preferences", "applied_time_ago": "Applied {time_ago}", "save_20": "Save up to 20%", "saved_cards": "Saved cards", "selected_job": "Selected job", "per_job": "Per job", "per_seat_per_month": "Per Seat/Month", "per_seat_per_year": "Per Seat/Year", "thanks_for_purchasing_item": "Thank you for purchasing {item}", "our_plan": "our plan", "candidate_rejected": "Candidate rejected", "selected_candidates_rejected": "Selected candidates rejected", "occasionally": "Occasionally", "considerable": "Considerable", "confirm_add_company_as_relation": "Are you sure you want to add this company as a {relation}?", "add_as_client": "Add as client", "add_as_vendor": "Add as vendor", "add_client": "Add client", "add_vendor": "Add vendor", "vendors": "Vend<PERSON>", "search_company_placeholder": "Search company by ID, title or username", "search_company_infobox_content": "Maximum requests are 50 per day, including up to 10 ulti-requests.", "confirm_cancel_request_to_company": "Are you sure you want to cancel your request?", "confirm_accept_pending_request": "Are you sure you want to accept this company’s {vendor} request?", "confirm_decline_pending_request": "Are you sure you want to decline this company’s {vendor} request?", "pending_request_infobox_content": "Requested to be the {client} of your company.", "requested_request_infobox_content": "You requested to be the {client} of this company.", "submit_job": "Submit Job", "person_from_page_requested_client_relationship": "{person}, {role} at {company}, has requested you to become their client.", "referral_users": "Referral users", "search_referral_user": "Search referral user", "referral_companies": "Referral current company", "search_referral_company": "Search referral company", "degrees": "Degree", "search_degree": "Search degree", "majors": "Major", "search_major": "Search major", "preferred_locations": "Preferred location", "search_preferred_location": "Search preferred location", "relocation_status": "Open to relocation", "race": "Race/Ethnicity", "disability_status": "Disability", "current_companies": "Current company", "search_current_company": "Search current company", "previous_companies": "Previous companies", "search_previous_company": "Search previous company", "last_activity": "Last activity", "employment_type": "Job type preferences", "work_place_type": "Workplace preferences", "past_year": "Past year", "past_6_months": "Past 6 months", "past_3_months": "Past 3 months", "bachelor": "Bachelor", "master": "Master", "doctoral": "Doctoral", "postdoctoral": "Postdoctoral", "middle_school": "Middle school", "high_school": "High school", "elementary": "Elementary", "diploma": "Diploma", "certificate": "Certificate", "honorary": "Honorary", "non": "Non", "______last_translate_key______": "** DEVELOPER NOTE: keep this key at MOST last line in this file to prevent confilicts **"}